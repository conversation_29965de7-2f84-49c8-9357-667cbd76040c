# Dependencies
node_modules
.pnpm-store

# Build
dist
.nuxt
.output
build

# Logs
server/logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Environment
.env
.env.*
!.env.example

# Configuration files (keep examples)
server-go/configs/config.yaml
server-go/configs/config.prod.yaml
server-go/configs/config.local.yaml
server-go/configs/config.dev.yaml
!server-go/configs/config.example.yaml
!server-go/configs/config.prod.example.yaml
!server-go/configs/env.example

# Build artifacts
server-go/build/
server-go/main
!server-go/build/.gitkeep

# IDE
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS
.DS_Store
Thumbs.db

# Testing
coverage
.nyc_output

# Temporary files
*.tmp
*.temp
.cache
static/admin/*
server-go/tmp/main

tmp

# Deployment environment files
deployment/production.env
!deployment/production.env.example
server-go/tmp/main
