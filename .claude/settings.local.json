{"permissions": {"allow": ["Bash(npm install:*)", "<PERSON><PERSON>(code:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git push:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "Bash(mv config-templates/ archive/)", "<PERSON><PERSON>(docker-compose:*)", "Bash(find:*)", "Bash(cp:*)", "Bash(rm:*)", "<PERSON><PERSON>(curl:*)", "Bash(grep:*)", "mcp__sequential-thinking__sequentialthinking", "<PERSON><PERSON>(mysql:*)", "<PERSON><PERSON>(make:*)", "mcp__playwright__browser_navigate", "mcp__playwright__browser_snapshot", "mcp__playwright__browser_click", "mcp__playwright__browser_type", "mcp__playwright__browser_evaluate", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(go install:*)", "Bash(migrate:*)", "Bash(go get:*)", "<PERSON><PERSON>(cat:*)", "Bash(git checkout:*)"], "deny": []}}