# ⚡ 快速升级指南

## 🎯 适用场景

你的线上环境已经使用旧的迁移系统执行到002版本，现在需要升级到新的Goose迁移系统。

## 🚀 5分钟快速升级

### 1. 检查当前状态

```bash
# 进入项目目录
cd server-go

# 检查升级状态
make upgrade-check
```

**预期输出**：
```
🔍 检查旧迁移系统状态...
✅ 检测到001版本已执行（表结构已优化）
✅ 检测到002版本已执行（services表已删除）
⚠️  003版本可能未执行
```

### 2. 执行升级

```bash
# 自动备份并升级
make upgrade-to-goose
```

**升级过程**：
- 🔍 检查数据库连接
- 💾 自动备份数据库
- 🔧 初始化Goose迁移系统
- ✅ 标记已完成的迁移
- 🔄 执行兼容性迁移

### 3. 验证结果

```bash
# 查看迁移状态
make goose-status

# 查看当前版本
make goose-version
```

**预期结果**：
```
📊 迁移状态:
Version: 1, Status: Applied, Source: goose
Version: 2, Status: Applied, Source: goose  
Version: 3, Status: Applied, Source: goose

📋 当前版本: 3
```

## 🔧 升级后使用

### 新的命令

```bash
# 执行迁移
make goose-up                   # 执行所有待执行的迁移
make goose-up VERSION=4         # 执行到版本4

# 回滚迁移
make goose-down                 # 回滚一个版本
make goose-down VERSION=2       # 回滚到版本2

# 查看状态
make goose-status               # 迁移状态
make goose-version              # 当前版本

# 管理工具
make goose-validate             # 验证迁移文件
make migrate-cli                # 构建CLI工具
```

### 版本对应关系

| 旧版本 | 新Goose版本 | 描述 |
|--------|-------------|------|
| 001 | 00001 | 表结构优化（已包含在基线中） |
| 002 | 00001 | 删除services表（已包含在基线中） |
| 003 | 00003 | 测试迁移（兼容性处理） |
| 未来 | 00004+ | 新的Goose迁移 |

## 🛡️ 安全保障

### 自动备份
升级过程会自动创建备份文件：
```
backup_goose_upgrade_YYYYMMDD_HHMMSS.sql
```

### 回滚方案
如果升级出现问题：
```bash
# 恢复备份
mysql -u root -p weizhi < backup_goose_upgrade_YYYYMMDD_HHMMSS.sql

# 或使用Goose回滚
make goose-down VERSION=2
```

## 🚨 故障排除

### 升级失败
```bash
# 检查详细状态
make upgrade-check

# 强制升级（跳过确认）
make upgrade-force
```

### 版本不一致
```bash
# 手动检查数据库
mysql -u root -p weizhi -e "SELECT * FROM goose_db_version;"

# 重新执行特定版本
make goose-up VERSION=3
```

## 📞 需要帮助？

- 📖 [完整升级指南](./LEGACY_UPGRADE_GUIDE.md)
- 🔧 [Goose使用文档](./README.md)
- 📋 [实施计划](./IMPLEMENTATION_PLAN.md)

---

🎉 **升级完成！** 现在你可以使用现代化的Goose迁移系统了！
