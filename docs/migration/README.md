# 🔄 数据库迁移指南

本文档介绍如何使用新的Goose数据库迁移系统替代GORM AutoMigrate。

## 📋 概述

### 为什么要迁移？

GORM AutoMigrate的局限性：
- ❌ 只能添加字段，不能删除
- ❌ 不能重命名字段或表
- ❌ 无版本控制和回滚机制
- ❌ 生产环境风险高
- ❌ 无法处理复杂数据迁移

### Goose的优势

- ✅ 支持SQL和Go函数迁移
- ✅ 完整的版本控制
- ✅ 安全的回滚机制
- ✅ 嵌入式迁移支持
- ✅ 事务性执行
- ✅ 生产环境友好

## 🚀 快速开始

### 1. 环境配置

复制环境配置文件：
```bash
cp .env.migration.example .env.migration
```

关键配置项：
```bash
# 启用Goose迁移
MIGRATION_ENABLED=true

# 使用嵌入式迁移
MIGRATION_USE_EMBEDDED=true

# 自动执行迁移
MIGRATION_AUTO_RUN=true
```

### 2. 查看迁移状态

```bash
# 查看当前迁移状态
make goose-status

# 查看当前版本
make goose-version
```

### 3. 执行迁移

```bash
# 执行所有待执行的迁移
make goose-up

# 执行到指定版本
make goose-up VERSION=2
```

## 📁 目录结构

```
server-go/
├── migrations/                 # 迁移文件目录
│   ├── embed.go               # 嵌入式迁移配置
│   ├── 00001_initial_schema.sql
│   └── 00002_add_file_uploads.go
├── internal/migration/        # 迁移管理模块
│   ├── config.go             # 配置管理
│   ├── manager.go            # 迁移管理器
│   └── service.go            # 迁移服务
├── cmd/migrate/              # CLI工具
│   └── main.go
└── scripts/migration/        # 迁移脚本
    ├── generate_baseline.sh
    └── validate_migrations.sh
```

## 🔧 使用方法

### 基本命令

```bash
# 查看帮助
make migration-help

# 执行迁移
make goose-up                  # 执行所有待执行的迁移
make goose-up VERSION=2        # 执行到版本2

# 回滚迁移
make goose-down                # 回滚一个版本
make goose-down VERSION=1      # 回滚到版本1

# 查看状态
make goose-status              # 查看迁移状态
make goose-version             # 查看当前版本

# 验证和管理
make goose-validate            # 验证迁移文件
make goose-baseline            # 生成基线迁移
make migrate-cli               # 构建CLI工具
```

### 创建新迁移

#### SQL迁移
```bash
# 使用Goose CLI创建SQL迁移
goose create add_user_table sql
```

创建的文件格式：
```sql
-- +goose Up
CREATE TABLE users (
    id bigint unsigned NOT NULL AUTO_INCREMENT,
    name varchar(255) NOT NULL,
    email varchar(255) NOT NULL,
    PRIMARY KEY (id)
);

-- +goose Down
DROP TABLE users;
```

#### Go迁移
```bash
# 使用Goose CLI创建Go迁移
goose create migrate_user_data go
```

创建的文件格式：
```go
package migrations

import (
    "database/sql"
    "github.com/pressly/goose/v3"
)

func init() {
    goose.AddMigration(upMigrateUserData, downMigrateUserData)
}

func upMigrateUserData(tx *sql.Tx) error {
    // 复杂的数据迁移逻辑
    return nil
}

func downMigrateUserData(tx *sql.Tx) error {
    // 回滚逻辑
    return nil
}
```

## ⚙️ 配置说明

### 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `MIGRATION_ENABLED` | `true` | 是否启用迁移 |
| `MIGRATION_TABLE_NAME` | `goose_db_version` | 迁移表名 |
| `MIGRATION_USE_EMBEDDED` | `true` | 使用嵌入式迁移 |
| `MIGRATION_AUTO_RUN` | `true` | 启动时自动执行 |
| `MIGRATION_AUTO_MIGRATE_FALLBACK` | `true` | 开发环境fallback |

### 环境策略

#### 开发环境
```bash
MIGRATION_ENABLED=false
MIGRATION_AUTO_MIGRATE_FALLBACK=true
APP_MODE=development
```
- 使用GORM AutoMigrate作为fallback
- 快速开发，无需手动管理迁移

#### 测试环境
```bash
MIGRATION_ENABLED=true
MIGRATION_AUTO_RUN=true
APP_MODE=testing
```
- 强制使用Goose迁移
- 验证迁移脚本正确性

#### 生产环境
```bash
MIGRATION_ENABLED=true
MIGRATION_AUTO_RUN=true
MIGRATION_AUTO_MIGRATE_FALLBACK=false
APP_MODE=production
```
- 严格使用Goose迁移
- 禁用AutoMigrate
- 完整的版本控制

## 🛡️ 安全措施

### 1. 自动备份
每次迁移前自动创建数据库备份：
```bash
# 备份文件格式
migration_backup_YYYYMMDD_HHMMSS.sql
```

### 2. 事务性执行
所有迁移在事务中执行，失败时自动回滚。

### 3. 验证机制
```bash
# 验证迁移文件
make goose-validate
```

### 4. 锁机制
防止并发执行迁移，确保数据一致性。

## 📊 监控和日志

### 迁移状态监控
```bash
# 检查迁移健康状态
curl http://localhost:3001/api/health
```

### 日志记录
迁移过程会记录详细日志：
```
🔄 开始初始化数据库迁移...
📊 迁移配置: Enabled=true, Environment=production, UseEmbedded=true
📋 当前数据库版本: 1
🚀 开始执行数据库迁移...
✅ 数据库迁移完成，耗时: 2.5s
📋 迁移后数据库版本: 2
```

## 🚨 故障排除

### 常见问题

#### 1. 迁移失败
```bash
# 查看详细错误信息
MIGRATION_VERBOSE=true make goose-up

# 检查迁移状态
make goose-status
```

#### 2. 版本冲突
```bash
# 强制设置版本（谨慎使用）
goose -dir migrations mysql "user:pass@/db" force 1
```

#### 3. 回滚问题
```bash
# 检查回滚脚本
make goose-validate

# 手动回滚
make goose-down VERSION=1
```

### 应急恢复

#### 1. 从备份恢复
```bash
# 恢复最新备份
mysql -u root -p weizhi < migration_backup_YYYYMMDD_HHMMSS.sql
```

#### 2. 重置迁移
```bash
# 重置所有迁移（危险操作！）
make goose-reset
```

## 📚 最佳实践

### 1. 迁移文件命名
```
00001_initial_schema.sql
00002_add_user_table.sql
00003_migrate_user_data.go
```

### 2. SQL迁移规范
- 使用 `IF NOT EXISTS` 创建表
- 避免直接删除数据
- 添加适当的索引

### 3. Go迁移规范
- 处理复杂数据迁移
- 包含完整的错误处理
- 提供详细的日志记录

### 4. 版本控制
- 每个功能一个迁移文件
- 保持迁移文件简洁
- 测试回滚脚本

## 🔄 迁移策略

### 从GORM AutoMigrate迁移

#### 阶段1：准备
1. 生成基线迁移
2. 验证迁移文件
3. 在开发环境测试

#### 阶段2：集成
1. 配置环境变量
2. 部署到测试环境
3. 验证功能正常

#### 阶段3：生产
1. 备份生产数据库
2. 部署新版本
3. 监控系统状态

#### 阶段4：清理
1. 移除AutoMigrate代码
2. 清理旧迁移脚本
3. 更新文档

## 📞 支持

如有问题，请：
1. 查看日志文件
2. 运行验证脚本
3. 检查配置文件
4. 联系开发团队
