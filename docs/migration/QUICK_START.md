# 🚀 Goose迁移快速开始指南

## 📋 概述

本指南帮助你快速上手新的Goose数据库迁移系统，替代原有的GORM AutoMigrate。

## ⚡ 5分钟快速开始

### 1. 检查环境

```bash
# 进入项目目录
cd server-go

# 检查Go版本
go version

# 检查依赖
go mod tidy
```

### 2. 配置环境变量

```bash
# 复制配置文件
cp .env.migration.example .env.migration

# 编辑配置（可选）
vim .env.migration
```

关键配置：
```bash
MIGRATION_ENABLED=true          # 启用Goose迁移
MIGRATION_USE_EMBEDDED=true     # 使用嵌入式迁移
MIGRATION_AUTO_RUN=true         # 启动时自动执行
```

### 3. 查看迁移状态

```bash
# 查看当前状态
make goose-status

# 查看当前版本
make goose-version
```

### 4. 执行迁移

```bash
# 执行所有待执行的迁移
make goose-up
```

### 5. 验证结果

```bash
# 再次查看状态
make goose-status

# 启动应用验证
make dev
```

## 🔧 常用命令

### 基础操作
```bash
# 查看帮助
make migration-help

# 执行迁移
make goose-up                   # 执行所有
make goose-up VERSION=2         # 执行到版本2

# 回滚迁移
make goose-down                 # 回滚一个版本
make goose-down VERSION=1       # 回滚到版本1

# 查看状态
make goose-status               # 迁移状态
make goose-version              # 当前版本
```

### 管理操作
```bash
# 验证迁移文件
make goose-validate

# 构建CLI工具
make migrate-cli

# 生成基线迁移
make goose-baseline
```

## 🌍 环境配置

### 开发环境（推荐配置）
```bash
# 使用AutoMigrate作为fallback，快速开发
MIGRATION_ENABLED=false
MIGRATION_AUTO_MIGRATE_FALLBACK=true
APP_MODE=development
```

### 测试环境
```bash
# 强制使用Goose，验证迁移脚本
MIGRATION_ENABLED=true
MIGRATION_AUTO_MIGRATE_FALLBACK=false
APP_MODE=testing
```

### 生产环境
```bash
# 严格使用Goose，完整版本控制
MIGRATION_ENABLED=true
MIGRATION_AUTO_MIGRATE_FALLBACK=false
APP_MODE=production
```

## 📁 文件结构

```
server-go/
├── migrations/                 # 迁移文件
│   ├── embed.go               # 嵌入式配置
│   ├── 00001_initial_schema.sql
│   └── 00002_add_file_uploads.go
├── internal/migration/        # 迁移模块
├── cmd/migrate/              # CLI工具
└── .env.migration.example    # 配置示例
```

## 🆕 创建新迁移

### SQL迁移
```bash
# 安装Goose CLI（如果还没有）
go install github.com/pressly/goose/v3/cmd/goose@latest

# 创建SQL迁移
cd migrations
goose create add_user_table sql
```

### Go迁移
```bash
# 创建Go迁移（用于复杂数据迁移）
cd migrations
goose create migrate_user_data go
```

## 🚨 故障排除

### 常见问题

#### 1. 迁移失败
```bash
# 查看详细错误
MIGRATION_VERBOSE=true make goose-up

# 检查迁移状态
make goose-status

# 验证迁移文件
make goose-validate
```

#### 2. 版本冲突
```bash
# 查看当前版本
make goose-version

# 手动设置版本（谨慎使用）
./build/migrate -command=force -version=1
```

#### 3. 连接问题
```bash
# 检查数据库连接
docker-compose ps mysql

# 检查环境变量
env | grep DB_
```

### 应急操作

#### 回滚到安全版本
```bash
# 回滚到版本1
make goose-down VERSION=1

# 或者重置所有迁移（危险！）
make goose-reset
```

#### 恢复备份
```bash
# 查看备份文件
ls -la backup_*.sql

# 恢复最新备份
mysql -u root -p weizhi < backup_YYYYMMDD_HHMMSS.sql
```

## 📊 监控和日志

### 查看迁移日志
```bash
# 应用日志
docker-compose logs -f server

# 数据库日志
docker-compose logs -f mysql
```

### 健康检查
```bash
# API健康检查
curl http://localhost:3001/api/health

# 迁移状态检查
make goose-status
```

## 🔄 从AutoMigrate迁移

### 如果你还在使用AutoMigrate

#### 步骤1：生成基线
```bash
# 从现有数据库生成基线迁移
make goose-baseline
```

#### 步骤2：验证基线
```bash
# 验证生成的迁移文件
make goose-validate

# 在测试环境验证
MIGRATION_ENABLED=true make goose-up
```

#### 步骤3：切换到Goose
```bash
# 更新环境配置
MIGRATION_ENABLED=true
MIGRATION_AUTO_MIGRATE_FALLBACK=false

# 重启应用
make restart
```

## 📚 进阶使用

### 自定义迁移表名
```bash
MIGRATION_TABLE_NAME=my_migrations
```

### 禁用嵌入式迁移
```bash
MIGRATION_USE_EMBEDDED=false
MIGRATION_DIRECTORY=./migrations
```

### 手动控制迁移
```bash
MIGRATION_AUTO_RUN=false
# 然后手动执行：make goose-up
```

## 📞 获取帮助

### 文档
- [完整文档](./README.md)
- [实施计划](./IMPLEMENTATION_PLAN.md)

### 命令帮助
```bash
# Makefile帮助
make help

# 迁移工具帮助
make migration-help

# CLI工具帮助
./build/migrate -help
```

### 支持渠道
- 技术文档：`docs/migration/`
- 示例代码：`migrations/`
- 问题反馈：项目Issue

---

🎉 **恭喜！** 你已经掌握了Goose迁移系统的基本使用。现在可以安全、可控地管理数据库变更了！
