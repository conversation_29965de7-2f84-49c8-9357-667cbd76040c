# 🔄 旧迁移系统升级指南

## 📋 概述

本指南帮助你从现有的旧迁移系统（shell脚本 + SQL文件）平滑升级到新的Goose迁移系统。

## 🔍 当前状态分析

### 旧迁移系统版本
根据你的线上环境，已执行的迁移版本：

| 版本 | 描述 | 文件 | 状态 |
|------|------|------|------|
| 001 | 优化表结构（删除冗余字段） | `001_optimize_table_structure.sql` | ✅ 已执行 |
| 002 | 删除services表 | `002_drop_services_table.sql` | ✅ 已执行 |
| 003 | 测试迁移 | `003_test_migration.sql` | ❓ 待确认 |

### 版本映射关系

| 旧版本 | 新Goose版本 | 处理方式 |
|--------|-------------|----------|
| 001 + 002 | 00001_initial_schema.sql | 包含在基线中 |
| 003 | 00003_migrate_from_legacy.go | 兼容性迁移 |
| 未来版本 | 00004+ | 新的Goose迁移 |

## 🚀 升级步骤

### 步骤1：检查当前状态

```bash
# 进入项目目录
cd server-go

# 检查当前数据库状态
./scripts/migration/upgrade_to_goose.sh --dry-run
```

**预期输出**：
```
🔍 检查旧迁移系统状态...
✅ 检测到001版本已执行（表结构已优化）
✅ 检测到002版本已执行（services表已删除）
⚠️  003版本可能未执行
```

### 步骤2：备份数据库

```bash
# 执行升级前备份
./scripts/migration/upgrade_to_goose.sh --backup --dry-run
```

### 步骤3：执行升级

```bash
# 执行完整升级
./scripts/migration/upgrade_to_goose.sh --backup

# 或者强制执行（跳过确认）
./scripts/migration/upgrade_to_goose.sh --backup --force
```

**升级过程**：
1. 🔍 检查数据库连接
2. 📊 分析当前迁移状态
3. 💾 备份数据库
4. 🔧 初始化Goose迁移表
5. ✅ 标记已完成的迁移
6. 🔄 执行兼容性迁移
7. ✅ 验证升级结果

### 步骤4：验证升级结果

```bash
# 查看Goose迁移状态
make goose-status

# 查看当前版本
make goose-version

# 验证应用启动
make dev
```

## 📊 升级后的状态

### Goose迁移版本

| 版本 | 描述 | 状态 |
|------|------|------|
| 00001 | 基线schema（包含001+002） | ✅ 已标记 |
| 00002 | 文件上传功能 | ✅ 已标记 |
| 00003 | 兼容性迁移（处理003） | 🔄 新执行 |

### 数据库表状态

**保留的表**：
- ✅ `admin_users` - 管理员用户
- ✅ `admin_roles` - 管理员角色
- ✅ `swipers` - 轮播图
- ✅ `news` - 新闻
- ✅ `project_cases` - 项目案例
- ✅ `part_platform` - 平台信息
- ✅ `file_uploads` - 文件上传

**删除的表**：
- ❌ `services` - 已删除（002版本）

**新增的表**：
- 🆕 `goose_db_version` - Goose版本管理
- 🆕 `legacy_migration_status` - 旧版本迁移状态
- 🆕 `migration_test` - 测试迁移表（如果003未执行）

## 🔧 升级后的使用

### 新的迁移命令

```bash
# 查看帮助
make migration-help

# 执行迁移
make goose-up                   # 执行所有待执行的迁移
make goose-up VERSION=4         # 执行到版本4

# 回滚迁移
make goose-down                 # 回滚一个版本
make goose-down VERSION=3       # 回滚到版本3

# 查看状态
make goose-status               # 迁移状态
make goose-version              # 当前版本
```

### 创建新迁移

```bash
# 安装Goose CLI（如果需要）
go install github.com/pressly/goose/v3/cmd/goose@latest

# 创建SQL迁移
cd migrations
goose create add_new_feature sql

# 创建Go迁移
goose create complex_data_migration go
```

## 🛡️ 安全措施

### 1. 自动备份
升级脚本会自动创建备份：
```
backup_goose_upgrade_YYYYMMDD_HHMMSS.sql
```

### 2. 兼容性检查
升级前会检查：
- 数据库连接状态
- 现有迁移版本
- 表结构完整性

### 3. 回滚机制
如果升级失败，可以：
```bash
# 恢复备份
mysql -u root -p weizhi < backup_goose_upgrade_YYYYMMDD_HHMMSS.sql

# 或使用Goose回滚
make goose-down VERSION=2
```

## 🚨 故障排除

### 常见问题

#### 1. 升级脚本执行失败
```bash
# 检查错误日志
./scripts/migration/upgrade_to_goose.sh --dry-run

# 检查数据库连接
mysql -u root -p -e "USE weizhi; SHOW TABLES;"
```

#### 2. Goose版本不正确
```bash
# 手动检查Goose表
mysql -u root -p weizhi -e "SELECT * FROM goose_db_version ORDER BY id;"

# 手动设置版本（谨慎使用）
./build/migrate -command=force -version=3
```

#### 3. 迁移状态不一致
```bash
# 检查legacy状态表
mysql -u root -p weizhi -e "SELECT * FROM legacy_migration_status;"

# 重新执行兼容性迁移
make goose-up VERSION=3
```

### 应急恢复

#### 完全回滚到旧系统
```bash
# 1. 恢复数据库备份
mysql -u root -p weizhi < backup_goose_upgrade_YYYYMMDD_HHMMSS.sql

# 2. 禁用Goose迁移
export MIGRATION_ENABLED=false
export MIGRATION_AUTO_MIGRATE_FALLBACK=true

# 3. 重启应用
make restart
```

## 📈 升级后的优势

### 1. 版本控制
- ✅ 完整的迁移历史
- ✅ 精确的版本管理
- ✅ 安全的回滚机制

### 2. 开发体验
- ✅ 统一的命令接口
- ✅ 自动化的迁移执行
- ✅ 详细的状态报告

### 3. 生产安全
- ✅ 事务性执行
- ✅ 自动备份机制
- ✅ 完整的错误处理

## 📞 支持

如果在升级过程中遇到问题：

1. **查看日志**：检查升级脚本的详细输出
2. **检查状态**：使用 `--dry-run` 模式分析问题
3. **恢复备份**：如有必要，从备份恢复
4. **联系支持**：提供详细的错误信息

---

🎉 **升级完成后**，你将拥有一个现代化、安全可控的数据库迁移系统！
