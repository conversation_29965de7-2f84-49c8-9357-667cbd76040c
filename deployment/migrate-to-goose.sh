#!/bin/bash

# 迁移到Goose数据库迁移系统的部署脚本
# 用于将现有项目从GORM AutoMigrate迁移到Goose

set -e

# 配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
SERVER_DIR="$PROJECT_ROOT/server-go"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "Goose迁移部署脚本"
    echo ""
    echo "用法:"
    echo "  $0 [选项] <环境>"
    echo ""
    echo "环境:"
    echo "  dev         开发环境部署"
    echo "  test        测试环境部署"
    echo "  prod        生产环境部署"
    echo ""
    echo "选项:"
    echo "  --dry-run   仅显示将要执行的操作，不实际执行"
    echo "  --backup    执行前备份数据库"
    echo "  --validate  验证迁移文件"
    echo "  --help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 --dry-run dev"
    echo "  $0 --backup prod"
    echo "  $0 --validate test"
}

# 检查依赖
check_dependencies() {
    log_step "检查依赖..."
    
    # 检查Go
    if ! command -v go &> /dev/null; then
        log_error "Go未安装"
        exit 1
    fi
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装"
        exit 1
    fi
    
    # 检查docker-compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "docker-compose未安装"
        exit 1
    fi
    
    log_info "✅ 依赖检查通过"
}

# 检查项目结构
check_project_structure() {
    log_step "检查项目结构..."
    
    local required_dirs=(
        "$SERVER_DIR"
        "$SERVER_DIR/internal"
        "$SERVER_DIR/migrations"
        "$SERVER_DIR/cmd/migrate"
    )
    
    for dir in "${required_dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            log_error "目录不存在: $dir"
            exit 1
        fi
    done
    
    local required_files=(
        "$SERVER_DIR/go.mod"
        "$SERVER_DIR/internal/migration/config.go"
        "$SERVER_DIR/internal/migration/manager.go"
        "$SERVER_DIR/cmd/migrate/main.go"
    )
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            log_error "文件不存在: $file"
            exit 1
        fi
    done
    
    log_info "✅ 项目结构检查通过"
}

# 安装Go依赖
install_dependencies() {
    log_step "安装Go依赖..."
    
    cd "$SERVER_DIR"
    
    # 添加Goose依赖
    if ! grep -q "github.com/pressly/goose/v3" go.mod; then
        log_info "添加Goose依赖..."
        go get github.com/pressly/goose/v3
    fi
    
    # 整理依赖
    go mod tidy
    
    log_info "✅ 依赖安装完成"
}

# 构建迁移CLI工具
build_migration_cli() {
    log_step "构建迁移CLI工具..."
    
    cd "$SERVER_DIR"
    
    if [ ! -d "build" ]; then
        mkdir -p build
    fi
    
    go build -o build/migrate cmd/migrate/main.go
    
    if [ -f "build/migrate" ]; then
        log_info "✅ 迁移CLI工具构建成功"
    else
        log_error "❌ 迁移CLI工具构建失败"
        exit 1
    fi
}

# 验证迁移文件
validate_migrations() {
    log_step "验证迁移文件..."
    
    cd "$SERVER_DIR"
    
    if [ -f "scripts/migration/validate_migrations.sh" ]; then
        chmod +x scripts/migration/validate_migrations.sh
        if ./scripts/migration/validate_migrations.sh; then
            log_info "✅ 迁移文件验证通过"
        else
            log_error "❌ 迁移文件验证失败"
            exit 1
        fi
    else
        log_warn "⚠️  验证脚本不存在，跳过验证"
    fi
}

# 备份数据库
backup_database() {
    local env=$1
    
    log_step "备份数据库 ($env)..."
    
    local backup_file="backup_$(date +%Y%m%d_%H%M%S).sql"
    
    case $env in
        "dev")
            docker-compose -f docker-compose.local.yml exec -T mysql \
                mysqldump -u root -p'Ydb3344%' weizhi > "$backup_file"
            ;;
        "prod")
            docker-compose -f docker-compose.prod.yml --env-file production.env exec -T mysql \
                mysqldump -u root -p'Ydb3344%' weizhi > "$backup_file"
            ;;
        *)
            log_warn "⚠️  未知环境，跳过备份"
            return
            ;;
    esac
    
    if [ -f "$backup_file" ]; then
        log_info "✅ 数据库备份完成: $backup_file"
    else
        log_error "❌ 数据库备份失败"
        exit 1
    fi
}

# 更新环境配置
update_environment_config() {
    local env=$1
    
    log_step "更新环境配置 ($env)..."
    
    case $env in
        "dev")
            # 开发环境：保留AutoMigrate作为fallback
            export MIGRATION_ENABLED=false
            export MIGRATION_AUTO_MIGRATE_FALLBACK=true
            export APP_MODE=development
            ;;
        "test")
            # 测试环境：强制使用Goose
            export MIGRATION_ENABLED=true
            export MIGRATION_AUTO_MIGRATE_FALLBACK=false
            export APP_MODE=testing
            ;;
        "prod")
            # 生产环境：严格使用Goose
            export MIGRATION_ENABLED=true
            export MIGRATION_AUTO_MIGRATE_FALLBACK=false
            export APP_MODE=production
            ;;
    esac
    
    log_info "✅ 环境配置更新完成"
}

# 执行迁移
run_migration() {
    local env=$1
    
    log_step "执行数据库迁移 ($env)..."
    
    cd "$SERVER_DIR"
    
    # 检查迁移状态
    log_info "检查当前迁移状态..."
    if ./build/migrate -command=status; then
        log_info "迁移状态检查完成"
    else
        log_warn "⚠️  无法获取迁移状态，可能是首次运行"
    fi
    
    # 执行迁移
    log_info "执行迁移..."
    if ./build/migrate -command=up; then
        log_info "✅ 迁移执行成功"
    else
        log_error "❌ 迁移执行失败"
        exit 1
    fi
    
    # 验证迁移结果
    log_info "验证迁移结果..."
    ./build/migrate -command=version
}

# 重启服务
restart_services() {
    local env=$1
    
    log_step "重启服务 ($env)..."
    
    case $env in
        "dev")
            docker-compose -f docker-compose.local.yml restart server
            ;;
        "prod")
            docker-compose -f docker-compose.prod.yml --env-file production.env restart server
            ;;
        *)
            log_warn "⚠️  未知环境，跳过服务重启"
            return
            ;;
    esac
    
    log_info "✅ 服务重启完成"
}

# 验证部署结果
verify_deployment() {
    local env=$1
    
    log_step "验证部署结果 ($env)..."
    
    # 等待服务启动
    sleep 10
    
    # 检查服务健康状态
    local health_url
    case $env in
        "dev")
            health_url="http://localhost:3001/api/health"
            ;;
        "prod")
            health_url="http://localhost:3001/api/health"
            ;;
        *)
            log_warn "⚠️  未知环境，跳过健康检查"
            return
            ;;
    esac
    
    if curl -f "$health_url" > /dev/null 2>&1; then
        log_info "✅ 服务健康检查通过"
    else
        log_error "❌ 服务健康检查失败"
        exit 1
    fi
}

# 主函数
main() {
    local dry_run=false
    local backup=false
    local validate=false
    local env=""
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --dry-run)
                dry_run=true
                shift
                ;;
            --backup)
                backup=true
                shift
                ;;
            --validate)
                validate=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            dev|test|prod)
                env=$1
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查环境参数
    if [ -z "$env" ]; then
        log_error "请指定环境: dev, test, 或 prod"
        show_help
        exit 1
    fi
    
    log_info "🚀 开始Goose迁移部署 (环境: $env)"
    
    if [ "$dry_run" = true ]; then
        log_info "🔍 DRY RUN 模式 - 仅显示操作，不实际执行"
    fi
    
    # 执行部署步骤
    check_dependencies
    check_project_structure
    
    if [ "$dry_run" = false ]; then
        install_dependencies
        build_migration_cli
        
        if [ "$validate" = true ]; then
            validate_migrations
        fi
        
        if [ "$backup" = true ]; then
            backup_database "$env"
        fi
        
        update_environment_config "$env"
        run_migration "$env"
        restart_services "$env"
        verify_deployment "$env"
        
        log_info "✅ Goose迁移部署完成！"
        log_info "📋 下一步："
        log_info "   1. 监控应用日志"
        log_info "   2. 验证功能正常"
        log_info "   3. 更新文档"
    else
        log_info "✅ DRY RUN 完成 - 所有检查通过"
    fi
}

# 执行主函数
main "$@"
