package model

// Swiper 轮播图模型（优化后）
type Swiper struct {
	BaseModel
	URL   string `gorm:"type:varchar(255);not null" json:"url"`   // 图片URL
	Title string `gorm:"type:varchar(255);not null" json:"title"` // 图片标题（用于alt属性）
	Order int    `gorm:"default:0" json:"order"`                  // 显示顺序
}

// TableName 自定义表名
func (Swiper) TableName() string {
	return "swipers"
}

// News 新闻模型
type News struct {
	BaseModel
	Title      string  `gorm:"type:varchar(255);not null" json:"title"`
	Content    string  `gorm:"type:text;not null" json:"content"`
	Image      *string `gorm:"type:varchar(255)" json:"image"`
	IsHomePage bool    `gorm:"default:false;column:is_home_page" json:"isHomePage"`
}

// ProjectCase 项目案例模型（优化后）
type ProjectCase struct {
	BaseModel
	URL string `gorm:"type:varchar(255);not null" json:"url"` // 案例图片URL
}

// Partner 合作伙伴模型
type Partner struct {
	BaseModel
	Name string `gorm:"type:varchar(255);not null" json:"name"`
	Logo string `gorm:"type:varchar(255);not null" json:"logo"`
}

// FriendLink 友情链接模型
type FriendLink struct {
	BaseModel
	Name  string `gorm:"type:varchar(255);not null" json:"name"`
	URL   string `gorm:"type:varchar(255);not null" json:"url"`
	Order int    `gorm:"default:0" json:"order"`
}

// TableName 自定义表名
func (FriendLink) TableName() string {
	return "friend_links"
}

// Recruitment 招聘信息模型（优化后）
type Recruitment struct {
	BaseModel
	Name        string  `gorm:"type:varchar(255);not null" json:"name"`       // 姓名/职位名称
	Location    string  `gorm:"type:varchar(255);not null" json:"location"`   // 工作地点
	Content     string  `gorm:"type:text;not null" json:"content"`            // 招聘内容
	Position    string  `gorm:"type:varchar(255);not null" json:"position"`   // 职位
	Department  string  `gorm:"type:varchar(255);not null" json:"department"` // 部门
	Description string  `gorm:"type:text;not null" json:"description"`        // 职位描述
	Requirement *string `gorm:"type:text" json:"requirement"`                 // 职位要求（可选）
}

// PartPlatform 平台模型（优化后）
type PartPlatform struct {
	BaseModel
	Name         string `gorm:"type:varchar(255);not null" json:"name"` // 平台名称
	URL          string `gorm:"type:varchar(255);not null" json:"url"`  // 平台图片URL
	Description  string `gorm:"type:text;not null" json:"description"`  // 平台描述
	Parameters   string `gorm:"type:text;not null" json:"parameters"`   // 技术参数
	Applications string `gorm:"type:text;not null" json:"applications"` // 应用场景
}

// TableName 自定义表名
func (PartPlatform) TableName() string {
	return "part_platform"
}

// PartPlatformExtension 零件平台扩展数据模型
type PartPlatformExtension struct {
	BaseModel
	PartPlatformID uint                `gorm:"not null" json:"part_platform_id"`
	Title          string              `gorm:"type:varchar(255);not null" json:"title"`
	Description    string              `gorm:"type:text" json:"description"`
	ImageURL       string              `gorm:"type:varchar(500)" json:"image_url"`
	SortOrder      int                 `gorm:"default:0" json:"sort_order"`
	Tables         []PartPlatformTable `gorm:"foreignKey:ExtensionID" json:"tables,omitempty"`
}

// TableName 自定义表名
func (PartPlatformExtension) TableName() string {
	return "part_platform_extension"
}

// PartPlatformTable 零件平台动态表格模型
type PartPlatformTable struct {
	BaseModel
	ExtensionID           uint                              `gorm:"not null" json:"extension_id"`
	Name                  string                            `gorm:"type:varchar(255);not null;column:table_name" json:"table_name"`
	CombinationTemplateID *uint                             `gorm:"column:combination_template_id" json:"combination_template_id"`
	CustomColumns         string                            `gorm:"type:json" json:"custom_columns"`
	SortOrder             int                               `gorm:"default:0" json:"sort_order"`
	CombinationTemplate   *CombinationTemplate              `gorm:"foreignKey:CombinationTemplateID" json:"combination_template,omitempty"`
	ColumnOverrides       []PartPlatformTableColumnOverride `gorm:"foreignKey:TableID" json:"column_overrides,omitempty"`
	Columns               []PartPlatformTableColumn         `gorm:"foreignKey:TableID" json:"columns,omitempty"`
	Rows                  []PartPlatformTableRow            `gorm:"foreignKey:TableID" json:"rows,omitempty"`
}

// TableName 自定义表名
func (PartPlatformTable) TableName() string {
	return "part_platform_table"
}

// PartPlatformTableColumn 零件平台动态表格列定义模型（保留用于兼容性）
type PartPlatformTableColumn struct {
	BaseModel
	TableID     uint   `gorm:"not null" json:"table_id"`
	ColumnName  string `gorm:"type:varchar(100);not null" json:"column_name"`
	ColumnLabel string `gorm:"type:varchar(255);not null" json:"column_label"`
	ColumnType  string `gorm:"type:varchar(50);default:'text'" json:"column_type"`
	SortOrder   int    `gorm:"default:0" json:"sort_order"`
}

// TableName 自定义表名
func (PartPlatformTableColumn) TableName() string {
	return "part_platform_table_column"
}

// PartPlatformTableColumnOverride 表格列覆盖配置模型
type PartPlatformTableColumnOverride struct {
	BaseModel
	TableID          uint            `gorm:"not null" json:"table_id"`
	ColumnTemplateID uint            `gorm:"not null" json:"column_template_id"`
	Action           string          `gorm:"type:varchar(20);not null" json:"action"` // 'hide', 'modify', 'add'
	CustomLabel      string          `gorm:"type:varchar(255)" json:"custom_label"`
	CustomConfig     string          `gorm:"type:json" json:"custom_config"`
	SortOrder        int             `gorm:"default:0" json:"sort_order"`
	ColumnTemplate   *ColumnTemplate `gorm:"foreignKey:ColumnTemplateID" json:"column_template,omitempty"`
}

// TableName 自定义表名
func (PartPlatformTableColumnOverride) TableName() string {
	return "part_platform_table_column_override"
}

// PartPlatformTableRow 零件平台动态表格行数据模型
type PartPlatformTableRow struct {
	BaseModel
	TableID   uint   `gorm:"not null" json:"table_id"`
	RowData   string `gorm:"type:json" json:"row_data"`
	SortOrder int    `gorm:"default:0" json:"sort_order"`
}

// TableName 自定义表名
func (PartPlatformTableRow) TableName() string {
	return "part_platform_table_row"
}

// PartPlatformSection 零件平台章节模型
type PartPlatformSection struct {
	BaseModel
	PartPlatformID uint                     `gorm:"not null" json:"part_platform_id"`
	Title          string                   `gorm:"type:varchar(255);not null" json:"title"`
	Description    string                   `gorm:"type:text" json:"description"`
	SortOrder      int                      `gorm:"default:0" json:"sort_order"`
	SubSections    []PartPlatformSubSection `gorm:"foreignKey:SectionID" json:"sub_sections,omitempty"`
}

// TableName 自定义表名
func (PartPlatformSection) TableName() string {
	return "part_platform_section"
}

// PartPlatformSubSection 零件平台子章节模型
type PartPlatformSubSection struct {
	BaseModel
	SectionID      uint                        `gorm:"not null" json:"section_id"`
	Title          string                      `gorm:"type:varchar(255);not null" json:"title"`
	ImageURL       string                      `gorm:"type:varchar(500)" json:"image_url"`
	Description    string                      `gorm:"type:text" json:"description"`
	SortOrder      int                         `gorm:"default:0" json:"sort_order"`
	Specifications []PartPlatformSpecification `gorm:"foreignKey:SubSectionID" json:"specifications,omitempty"`
}

// TableName 自定义表名
func (PartPlatformSubSection) TableName() string {
	return "part_platform_sub_section"
}

// PartPlatformSpecification 零件平台规格模型
type PartPlatformSpecification struct {
	BaseModel
	SubSectionID  uint   `gorm:"not null" json:"sub_section_id"`
	Model         string `gorm:"type:varchar(100);not null" json:"model"`
	QmaxLmin      string `gorm:"type:varchar(50)" json:"qmax_lmin"`
	QmaxGPM       string `gorm:"type:varchar(50)" json:"qmax_gpm"`
	PmaxBar       string `gorm:"type:varchar(50)" json:"pmax_bar"`
	PmaxPSI       string `gorm:"type:varchar(50)" json:"pmax_psi"`
	Specification string `gorm:"type:varchar(100)" json:"specification"`
	SamplePage    string `gorm:"type:varchar(50)" json:"sample_page"`
	SortOrder     int    `gorm:"default:0" json:"sort_order"`
}

// TableName 自定义表名
func (PartPlatformSpecification) TableName() string {
	return "part_platform_specification"
}

// ColumnTemplate 列模板模型
type ColumnTemplate struct {
	BaseModel
	Name            string                `gorm:"type:varchar(100);not null;uniqueIndex" json:"name"`
	Label           string                `gorm:"type:varchar(100);not null" json:"label"`
	Type            string                `gorm:"type:varchar(20);not null" json:"type"`
	Category        string                `gorm:"type:varchar(50);not null" json:"category"`
	Description     string                `gorm:"type:text" json:"description"`
	IsRequired      bool                  `gorm:"default:false" json:"is_required"`
	IsCommon        bool                  `gorm:"default:false" json:"is_common"`
	DefaultValue    string                `gorm:"type:text" json:"default_value"`
	Options         string                `gorm:"type:json" json:"options"`
	ValidationRules string                `gorm:"type:json" json:"validation_rules"`
	SortOrder       int                   `gorm:"default:0" json:"sort_order"`
	IsSystem        bool                  `gorm:"default:true" json:"is_system"`
	Combinations    []CombinationTemplate `gorm:"many2many:combination_template_columns;" json:"combinations,omitempty"`
}

// TableName 自定义表名
func (ColumnTemplate) TableName() string {
	return "column_templates"
}

// CombinationTemplate 组合模板模型
type CombinationTemplate struct {
	BaseModel
	Name        string           `gorm:"type:varchar(100);not null" json:"name"`
	Description string           `gorm:"type:text" json:"description"`
	Category    string           `gorm:"type:varchar(50)" json:"category"`
	Scenarios   string           `gorm:"type:json" json:"scenarios"`
	IsSystem    bool             `gorm:"default:true" json:"is_system"`
	CreatedBy   *uint            `gorm:"" json:"created_by"`
	SortOrder   int              `gorm:"default:0" json:"sort_order"`
	Columns     []ColumnTemplate `gorm:"many2many:combination_template_columns;" json:"columns,omitempty"`
}

// TableName 自定义表名
func (CombinationTemplate) TableName() string {
	return "combination_templates"
}

// CombinationTemplateColumn 组合模板列关联模型
type CombinationTemplateColumn struct {
	CombinationTemplateID uint  `gorm:"primaryKey" json:"combination_template_id"`
	ColumnTemplateID      uint  `gorm:"primaryKey" json:"column_template_id"`
	SortOrder             int   `gorm:"default:0" json:"sort_order"`
	CreatedAt             int64 `gorm:"autoCreateTime" json:"created_at"`
}

// TableName 自定义表名
func (CombinationTemplateColumn) TableName() string {
	return "combination_template_columns"
}

// FileUpload 文件上传模型
type FileUpload struct {
	BaseModel
	OriginalName string `gorm:"type:varchar(255);not null" json:"originalName"`  // 原始文件名
	FileName     string `gorm:"type:varchar(255);not null" json:"fileName"`      // 存储文件名
	FilePath     string `gorm:"type:varchar(500);not null" json:"filePath"`      // 文件路径
	FileSize     int64  `gorm:"not null" json:"fileSize"`                        // 文件大小（字节）
	MimeType     string `gorm:"type:varchar(100);not null" json:"mimeType"`      // 文件类型
	BucketName   string `gorm:"type:varchar(100);not null" json:"bucketName"`    // 存储桶名称
	CosURL       string `gorm:"type:varchar(500);not null" json:"cosUrl"`        // COS访问URL
	UploadedBy   uint   `gorm:"not null" json:"uploadedBy"`                      // 上传用户ID
	Module       string `gorm:"type:varchar(50);not null" json:"module"`         // 所属模块
	Status       string `gorm:"type:varchar(20);default:'active'" json:"status"` // 文件状态
}

// TableName 自定义表名
func (FileUpload) TableName() string {
	return "file_uploads"
}
