package database

import (
	"context"
	"fmt"
	"log"
	"weishi-server/internal/config"
	"weishi-server/internal/migration"
	"weishi-server/internal/model"

	// 导入迁移文件以注册嵌入式迁移
	_ "weishi-server/migrations"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func New(cfg config.DatabaseConfig) (*gorm.DB, error) {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=%s&parseTime=True&loc=Local",
		cfg.Username,
		cfg.Password,
		cfg.Host,
		cfg.Port,
		cfg.Database,
		cfg.Charset,
	)

	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// 初始化数据库迁移
	if err := initializeMigration(db); err != nil {
		return nil, fmt.Errorf("failed to initialize migration: %w", err)
	}

	return db, nil
}

// initializeMigration 初始化数据库迁移
func initializeMigration(db *gorm.DB) error {
	// 创建迁移配置
	migrationConfig := migration.NewConfig()

	// 创建迁移服务
	migrationService, err := migration.NewService(db, migrationConfig)
	if err != nil {
		return fmt.Errorf("failed to create migration service: %w", err)
	}

	// 初始化迁移
	ctx := context.Background()
	if err := migrationService.Initialize(ctx); err != nil {
		return fmt.Errorf("failed to initialize migration: %w", err)
	}

	log.Printf("✅ 数据库迁移初始化完成")
	return nil
}

func autoMigrate(db *gorm.DB) error {
	return db.AutoMigrate(
		// 用户相关
		// &model.User{},
		&model.AdminUser{},
		&model.AdminRole{},
		&model.AdminPermission{},
		&model.AdminUserRole{},
		&model.AdminRolePermission{},
		&model.AdminLog{},

		// 内容管理
		&model.Swiper{},
		&model.News{},
		&model.ProjectCase{},
		&model.Partner{},
		&model.FriendLink{},
		&model.Recruitment{},
		&model.PartPlatform{},
	)
}
