package handler

import (
	"log"
	"strconv"
	"strings"
	"weishi-server/internal/model"
	"weishi-server/internal/service"
	"weishi-server/pkg/response"

	"github.com/gin-gonic/gin"
)

// ContentService 接口定义（临时）
type ContentService interface {
	GetAllNews() (interface{}, error)
	GetHomePageNews() (interface{}, error)
	GetNewsByID(id uint) (interface{}, error)
	GetAllServices() (interface{}, error)
	GetAllSwipers() ([]model.Swiper, error)
	GetSwiperByID(id uint) (*model.Swiper, error)
	CreateSwiper(swiper *model.Swiper) error
	UpdateSwiper(id uint, swiper *model.Swiper) error
	DeleteSwiper(id uint) error
	GetAllPartners() (interface{}, error)
	GetAllFriendLinks() (interface{}, error)
	GetFriendLinkByID(id uint) (*model.FriendLink, error)
	CreateFriendLink(friendLink *model.FriendLink) error
	UpdateFriendLink(id uint, friendLink *model.FriendLink) error
	DeleteFriendLink(id uint) error

	// 招聘信息相关方法
	GetAllRecruitments() (interface{}, error)
	GetRecruitmentByID(id uint) (*model.Recruitment, error)
	CreateRecruitment(recruitment *model.Recruitment) error
	UpdateRecruitment(id uint, recruitment *model.Recruitment) error
	DeleteRecruitment(id uint) error

	// 平台相关方法
	GetAllPartPlatforms() (interface{}, error)
	GetPartPlatformByID(id uint) (*model.PartPlatform, error)
	CreatePartPlatform(platform *model.PartPlatform) error
	UpdatePartPlatform(id uint, platform *model.PartPlatform) error
	DeletePartPlatform(id uint) error

	GetAllProjectCases() (interface{}, error)
}

type ContentHandler struct {
	contentService service.ContentService
}

func NewContentHandler(contentService service.ContentService) *ContentHandler {
	return &ContentHandler{
		contentService: contentService,
	}
}

// GetNews 获取新闻列表（支持分页）
func (h *ContentHandler) GetNews(c *gin.Context) {
	// 获取分页参数
	pageStr := c.DefaultQuery("page", "1")
	sizeStr := c.DefaultQuery("size", "10")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	size, err := strconv.Atoi(sizeStr)
	if err != nil || size < 1 || size > 100 {
		size = 10
	}

	// 获取搜索参数
	keyword := c.Query("keyword")
	isHomePageStr := c.Query("isHomePage")

	// 获取所有新闻
	allNews, err := h.contentService.GetAllNews()
	if err != nil {
		response.InternalServerError(c, "获取新闻列表失败")
		return
	}

	// 过滤新闻
	var filteredNews []model.News
	for _, news := range allNews {
		// 关键词过滤
		if keyword != "" {
			if !strings.Contains(news.Title, keyword) {
				continue
			}
		}

		// 首页显示过滤
		if isHomePageStr != "" {
			if isHomePageStr == "true" && !news.IsHomePage {
				continue
			}
			if isHomePageStr == "false" && news.IsHomePage {
				continue
			}
		}

		filteredNews = append(filteredNews, news)
	}

	// 计算总数
	total := len(filteredNews)

	// 计算分页
	offset := (page - 1) * size
	end := offset + size

	// 处理边界情况
	if offset >= total {
		result := map[string]interface{}{
			"list":      []model.News{},
			"total":     total,
			"page":      page,
			"page_size": size,
		}
		response.Success(c, result)
		return
	}

	if end > total {
		end = total
	}

	// 分页数据
	pagedNews := filteredNews[offset:end]

	// 构造响应数据
	result := map[string]interface{}{
		"list":      pagedNews,
		"total":     total,
		"page":      page,
		"page_size": size,
	}

	response.Success(c, result)
}

// GetHomePageNews 获取首页新闻
func (h *ContentHandler) GetHomePageNews(c *gin.Context) {
	news, err := h.contentService.GetHomePageNews()
	if err != nil {
		response.InternalServerError(c, "获取首页新闻失败")
		return
	}
	response.Success(c, news)
}

// GetNewsDetail 获取新闻详情
func (h *ContentHandler) GetNewsDetail(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的新闻ID")
		return
	}

	news, err := h.contentService.GetNewsByID(uint(id))
	if err != nil {
		response.InternalServerError(c, "获取新闻详情失败")
		return
	}
	response.Success(c, news)
}

// CreateNews 创建新闻
func (h *ContentHandler) CreateNews(c *gin.Context) {
	var news model.News
	if err := c.ShouldBindJSON(&news); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	if err := h.contentService.CreateNews(&news); err != nil {
		response.InternalServerError(c, "创建新闻失败")
		return
	}

	response.SuccessWithMsg(c, news, "创建新闻成功")
}

// UpdateNews 更新新闻
func (h *ContentHandler) UpdateNews(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的新闻ID")
		return
	}

	var news model.News
	if err := c.ShouldBindJSON(&news); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	if err := h.contentService.UpdateNews(uint(id), &news); err != nil {
		response.InternalServerError(c, "更新新闻失败")
		return
	}

	response.SuccessWithMsg(c, news, "更新新闻成功")
}

// DeleteNews 删除新闻
func (h *ContentHandler) DeleteNews(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的新闻ID")
		return
	}

	if err := h.contentService.DeleteNews(uint(id)); err != nil {
		response.InternalServerError(c, "删除新闻失败")
		return
	}

	response.SuccessWithMsg(c, nil, "删除新闻成功")
}

// SetNewsHomePage 设置新闻首页显示
func (h *ContentHandler) SetNewsHomePage(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的新闻ID")
		return
	}

	var req struct {
		IsHomePage bool `json:"isHomePage"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	if err := h.contentService.SetNewsHomePage(uint(id), req.IsHomePage); err != nil {
		response.InternalServerError(c, "设置首页显示失败")
		return
	}

	response.SuccessWithMsg(c, nil, "设置成功")
}

// GetSwipers 获取轮播图列表（支持分页）
func (h *ContentHandler) GetSwipers(c *gin.Context) {
	// 获取分页参数
	pageStr := c.DefaultQuery("page", "1")
	sizeStr := c.DefaultQuery("size", "10")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	size, err := strconv.Atoi(sizeStr)
	if err != nil || size < 1 || size > 100 {
		size = 10
	}

	// 获取所有轮播图
	swipers, err := h.contentService.GetAllSwipers()
	if err != nil {
		response.InternalServerError(c, "获取轮播图列表失败")
		return
	}

	// 计算总数
	total := len(swipers)

	// 计算分页
	offset := (page - 1) * size
	end := offset + size

	// 处理边界情况
	if offset >= total {
		// 返回空数据
		result := map[string]interface{}{
			"list":      []model.Swiper{},
			"total":     total,
			"page":      page,
			"page_size": size,
		}
		response.Success(c, result)
		return
	}

	if end > total {
		end = total
	}

	// 分页数据
	pagedSwipers := swipers[offset:end]

	// 构造响应数据
	result := map[string]interface{}{
		"list":      pagedSwipers,
		"total":     total,
		"page":      page,
		"page_size": size,
	}

	response.Success(c, result)
}

// GetSwiperByID 获取轮播图详情
func (h *ContentHandler) GetSwiperByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的轮播图ID")
		return
	}

	swiper, err := h.contentService.GetSwiperByID(uint(id))
	if err != nil {
		response.InternalServerError(c, "获取轮播图详情失败")
		return
	}
	response.Success(c, swiper)
}

// CreateSwiper 创建轮播图
func (h *ContentHandler) CreateSwiper(c *gin.Context) {
	var swiper model.Swiper
	if err := c.ShouldBindJSON(&swiper); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	// 注意：已移除Status字段，轮播图默认为激活状态

	if err := h.contentService.CreateSwiper(&swiper); err != nil {
		response.InternalServerError(c, "创建轮播图失败")
		return
	}

	response.SuccessWithMsg(c, swiper, "创建轮播图成功")
}

// UpdateSwiper 更新轮播图
func (h *ContentHandler) UpdateSwiper(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的轮播图ID")
		return
	}

	var swiper model.Swiper
	if err := c.ShouldBindJSON(&swiper); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	if err := h.contentService.UpdateSwiper(uint(id), &swiper); err != nil {
		response.InternalServerError(c, "更新轮播图失败")
		return
	}

	response.SuccessWithMsg(c, swiper, "更新轮播图成功")
}

// DeleteSwiper 删除轮播图
func (h *ContentHandler) DeleteSwiper(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的轮播图ID")
		return
	}

	if err := h.contentService.DeleteSwiper(uint(id)); err != nil {
		response.InternalServerError(c, "删除轮播图失败")
		return
	}

	response.SuccessWithMsg(c, nil, "删除轮播图成功")
}

// UpdateSwiperOrder 更新轮播图排序
func (h *ContentHandler) UpdateSwiperOrder(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的轮播图ID")
		return
	}

	var req struct {
		Order int `json:"order" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	// 获取现有轮播图
	swiper, err := h.contentService.GetSwiperByID(uint(id))
	if err != nil {
		response.InternalServerError(c, "获取轮播图失败")
		return
	}

	// 更新排序
	swiper.Order = req.Order
	if err := h.contentService.UpdateSwiper(uint(id), swiper); err != nil {
		response.InternalServerError(c, "更新轮播图排序失败")
		return
	}

	response.SuccessWithMsg(c, swiper, "更新排序成功")
}

// GetPartners 获取合作伙伴列表（支持分页）
func (h *ContentHandler) GetPartners(c *gin.Context) {
	// 获取分页参数
	pageStr := c.DefaultQuery("page", "1")
	sizeStr := c.DefaultQuery("size", "10")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	size, err := strconv.Atoi(sizeStr)
	if err != nil || size < 1 || size > 100 {
		size = 10
	}

	// 获取搜索参数
	keyword := c.Query("keyword")

	// 获取所有合作伙伴
	allPartners, err := h.contentService.GetAllPartners()
	if err != nil {
		response.InternalServerError(c, "获取合作伙伴列表失败")
		return
	}

	// 过滤合作伙伴
	var filteredPartners []model.Partner
	for _, partner := range allPartners {
		// 关键词过滤
		if keyword != "" {
			if !strings.Contains(partner.Name, keyword) {
				continue
			}
		}

		filteredPartners = append(filteredPartners, partner)
	}

	// 计算总数
	total := len(filteredPartners)

	// 计算分页
	offset := (page - 1) * size
	end := offset + size

	// 处理边界情况
	if offset >= total {
		result := map[string]interface{}{
			"list":      []model.Partner{},
			"total":     total,
			"page":      page,
			"page_size": size,
		}
		response.Success(c, result)
		return
	}

	if end > total {
		end = total
	}

	// 分页数据
	pagedPartners := filteredPartners[offset:end]

	// 构造响应数据
	result := map[string]interface{}{
		"list":      pagedPartners,
		"total":     total,
		"page":      page,
		"page_size": size,
	}

	response.Success(c, result)
}

// GetPartnerDetail 获取合作伙伴详情
func (h *ContentHandler) GetPartnerDetail(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的合作伙伴ID")
		return
	}

	partner, err := h.contentService.GetPartnerByID(uint(id))
	if err != nil {
		response.InternalServerError(c, "获取合作伙伴详情失败")
		return
	}
	response.Success(c, partner)
}

// CreatePartner 创建合作伙伴
func (h *ContentHandler) CreatePartner(c *gin.Context) {
	var partner model.Partner
	if err := c.ShouldBindJSON(&partner); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	if err := h.contentService.CreatePartner(&partner); err != nil {
		response.InternalServerError(c, "创建合作伙伴失败")
		return
	}

	response.SuccessWithMsg(c, partner, "创建合作伙伴成功")
}

// UpdatePartner 更新合作伙伴
func (h *ContentHandler) UpdatePartner(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的合作伙伴ID")
		return
	}

	var partner model.Partner
	if err := c.ShouldBindJSON(&partner); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	if err := h.contentService.UpdatePartner(uint(id), &partner); err != nil {
		response.InternalServerError(c, "更新合作伙伴失败")
		return
	}

	response.SuccessWithMsg(c, partner, "更新合作伙伴成功")
}

// DeletePartner 删除合作伙伴
func (h *ContentHandler) DeletePartner(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的合作伙伴ID")
		return
	}

	if err := h.contentService.DeletePartner(uint(id)); err != nil {
		response.InternalServerError(c, "删除合作伙伴失败")
		return
	}

	response.SuccessWithMsg(c, nil, "删除合作伙伴成功")
}

// GetFriendLinks 获取友情链接列表（支持分页）
func (h *ContentHandler) GetFriendLinks(c *gin.Context) {
	// 获取分页参数
	pageStr := c.DefaultQuery("page", "1")
	sizeStr := c.DefaultQuery("size", "10")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	size, err := strconv.Atoi(sizeStr)
	if err != nil || size < 1 || size > 100 {
		size = 10
	}

	// 获取搜索参数
	keyword := c.Query("keyword")

	// 获取所有友情链接
	allFriendLinks, err := h.contentService.GetAllFriendLinks()
	if err != nil {
		response.InternalServerError(c, "获取友情链接列表失败")
		return
	}

	// 过滤友情链接
	var filteredFriendLinks []model.FriendLink
	for _, friendLink := range allFriendLinks {
		// 关键词过滤
		if keyword != "" {
			if !strings.Contains(friendLink.Name, keyword) {
				continue
			}
		}

		filteredFriendLinks = append(filteredFriendLinks, friendLink)
	}

	// 计算总数
	total := len(filteredFriendLinks)

	// 计算分页
	offset := (page - 1) * size
	end := offset + size

	// 处理边界情况
	if offset >= total {
		result := map[string]interface{}{
			"list":      []model.FriendLink{},
			"total":     total,
			"page":      page,
			"page_size": size,
		}
		response.Success(c, result)
		return
	}

	if end > total {
		end = total
	}

	// 分页数据
	pagedFriendLinks := filteredFriendLinks[offset:end]

	// 构造响应数据
	result := map[string]interface{}{
		"list":      pagedFriendLinks,
		"total":     total,
		"page":      page,
		"page_size": size,
	}

	response.Success(c, result)
}

// GetFriendLinkDetail 获取友情链接详情
func (h *ContentHandler) GetFriendLinkDetail(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的友情链接ID")
		return
	}

	friendLink, err := h.contentService.GetFriendLinkByID(uint(id))
	if err != nil {
		response.InternalServerError(c, "获取友情链接详情失败")
		return
	}
	response.Success(c, friendLink)
}

// CreateFriendLink 创建友情链接
func (h *ContentHandler) CreateFriendLink(c *gin.Context) {
	var friendLink model.FriendLink
	if err := c.ShouldBindJSON(&friendLink); err != nil {
		log.Printf("CreateFriendLink bind error: %v", err)
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	log.Printf("CreateFriendLink request data: %+v", friendLink)

	if err := h.contentService.CreateFriendLink(&friendLink); err != nil {
		log.Printf("CreateFriendLink service error: %v", err)
		response.InternalServerError(c, "创建友情链接失败: "+err.Error())
		return
	}

	response.SuccessWithMsg(c, friendLink, "创建友情链接成功")
}

// UpdateFriendLink 更新友情链接
func (h *ContentHandler) UpdateFriendLink(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的友情链接ID")
		return
	}

	var friendLink model.FriendLink
	if err := c.ShouldBindJSON(&friendLink); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	if err := h.contentService.UpdateFriendLink(uint(id), &friendLink); err != nil {
		response.InternalServerError(c, "更新友情链接失败")
		return
	}

	response.SuccessWithMsg(c, friendLink, "更新友情链接成功")
}

// DeleteFriendLink 删除友情链接
func (h *ContentHandler) DeleteFriendLink(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的友情链接ID")
		return
	}

	if err := h.contentService.DeleteFriendLink(uint(id)); err != nil {
		response.InternalServerError(c, "删除友情链接失败")
		return
	}

	response.SuccessWithMsg(c, nil, "删除友情链接成功")
}

// UpdateFriendLinkOrder 更新友情链接排序
func (h *ContentHandler) UpdateFriendLinkOrder(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的友情链接ID")
		return
	}

	var req struct {
		Order int `json:"order" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	// 获取现有友情链接
	friendLink, err := h.contentService.GetFriendLinkByID(uint(id))
	if err != nil {
		response.InternalServerError(c, "获取友情链接失败")
		return
	}

	// 更新排序
	friendLink.Order = req.Order
	if err := h.contentService.UpdateFriendLink(uint(id), friendLink); err != nil {
		response.InternalServerError(c, "更新友情链接排序失败")
		return
	}

	response.SuccessWithMsg(c, friendLink, "更新排序成功")
}

// GetRecruitments 获取招聘信息列表（支持分页）
func (h *ContentHandler) GetRecruitments(c *gin.Context) {
	// 获取分页参数
	pageStr := c.DefaultQuery("page", "1")
	sizeStr := c.DefaultQuery("size", "10")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	size, err := strconv.Atoi(sizeStr)
	if err != nil || size < 1 || size > 100 {
		size = 10
	}

	// 获取搜索参数
	keyword := c.Query("keyword")
	location := c.Query("location")

	// 获取所有招聘信息
	allRecruitments, err := h.contentService.GetAllRecruitments()
	if err != nil {
		response.InternalServerError(c, "获取招聘信息列表失败")
		return
	}

	// 过滤招聘信息
	var filteredRecruitments []model.Recruitment
	for _, recruitment := range allRecruitments {
		// 关键词过滤（职位名称）
		if keyword != "" {
			if !strings.Contains(recruitment.Name, keyword) {
				continue
			}
		}

		// 工作地点过滤
		if location != "" {
			if !strings.Contains(recruitment.Location, location) {
				continue
			}
		}

		filteredRecruitments = append(filteredRecruitments, recruitment)
	}

	// 计算总数
	total := len(filteredRecruitments)

	// 计算分页
	offset := (page - 1) * size
	end := offset + size

	// 处理边界情况
	if offset >= total {
		result := map[string]interface{}{
			"list":      []model.Recruitment{},
			"total":     total,
			"page":      page,
			"page_size": size,
		}
		response.Success(c, result)
		return
	}

	if end > total {
		end = total
	}

	// 分页数据
	pagedRecruitments := filteredRecruitments[offset:end]

	// 构造响应数据
	result := map[string]interface{}{
		"list":      pagedRecruitments,
		"total":     total,
		"page":      page,
		"page_size": size,
	}

	response.Success(c, result)
}

// GetRecruitmentDetail 获取招聘信息详情
func (h *ContentHandler) GetRecruitmentDetail(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的招聘信息ID")
		return
	}

	recruitment, err := h.contentService.GetRecruitmentByID(uint(id))
	if err != nil {
		response.InternalServerError(c, "获取招聘信息详情失败")
		return
	}
	response.Success(c, recruitment)
}

// CreateRecruitment 创建招聘信息
func (h *ContentHandler) CreateRecruitment(c *gin.Context) {
	log.Printf("CreateRecruitment called")

	var recruitment model.Recruitment
	if err := c.ShouldBindJSON(&recruitment); err != nil {
		log.Printf("CreateRecruitment bind error: %v", err)
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	log.Printf("CreateRecruitment request data: %+v", recruitment)

	if err := h.contentService.CreateRecruitment(&recruitment); err != nil {
		log.Printf("CreateRecruitment service error: %v", err)
		response.InternalServerError(c, "创建招聘信息失败: "+err.Error())
		return
	}

	response.SuccessWithMsg(c, recruitment, "创建招聘信息成功")
}

// UpdateRecruitment 更新招聘信息
func (h *ContentHandler) UpdateRecruitment(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的招聘信息ID")
		return
	}

	var recruitment model.Recruitment
	if err := c.ShouldBindJSON(&recruitment); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	if err := h.contentService.UpdateRecruitment(uint(id), &recruitment); err != nil {
		response.InternalServerError(c, "更新招聘信息失败")
		return
	}

	response.SuccessWithMsg(c, recruitment, "更新招聘信息成功")
}

// DeleteRecruitment 删除招聘信息
func (h *ContentHandler) DeleteRecruitment(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的招聘信息ID")
		return
	}

	if err := h.contentService.DeleteRecruitment(uint(id)); err != nil {
		response.InternalServerError(c, "删除招聘信息失败")
		return
	}

	response.SuccessWithMsg(c, nil, "删除招聘信息成功")
}

// GetPartPlatforms 获取平台列表（支持分页和搜索）
func (h *ContentHandler) GetPartPlatforms(c *gin.Context) {
	// 获取分页参数
	pageStr := c.DefaultQuery("page", "1")
	sizeStr := c.DefaultQuery("size", "10")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	size, err := strconv.Atoi(sizeStr)
	if err != nil || size < 1 || size > 100 {
		size = 10
	}

	// 获取搜索参数
	keyword := c.Query("keyword")

	// 获取所有平台
	allPlatforms, err := h.contentService.GetAllPartPlatforms()
	if err != nil {
		response.InternalServerError(c, "获取平台列表失败")
		return
	}

	// 过滤平台
	var filteredPlatforms []model.PartPlatform
	for _, platform := range allPlatforms {
		// 关键词过滤（搜索平台名称）
		if keyword != "" {
			if !strings.Contains(platform.Name, keyword) {
				continue
			}
		}

		filteredPlatforms = append(filteredPlatforms, platform)
	}

	// 计算总数
	total := len(filteredPlatforms)

	// 计算分页
	offset := (page - 1) * size
	end := offset + size

	// 处理边界情况
	if offset >= total {
		result := map[string]interface{}{
			"list":      []model.PartPlatform{},
			"total":     total,
			"page":      page,
			"page_size": size,
		}
		response.Success(c, result)
		return
	}

	if end > total {
		end = total
	}

	// 分页数据
	pagedPlatforms := filteredPlatforms[offset:end]

	// 构造响应数据
	result := map[string]interface{}{
		"list":      pagedPlatforms,
		"total":     total,
		"page":      page,
		"page_size": size,
	}

	response.Success(c, result)
}

// GetPartPlatformDetail 获取平台详情
func (h *ContentHandler) GetPartPlatformDetail(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的平台ID")
		return
	}

	// 获取基础平台信息和扩展数据
	platform, extensions, err := h.contentService.GetPartPlatformWithExtensions(uint(id))
	if err != nil {
		response.InternalServerError(c, "获取平台详情失败")
		return
	}

	// 构造响应数据
	result := map[string]interface{}{
		"platform":   platform,
		"extensions": extensions,
	}
	response.Success(c, result)
}

// CreatePartPlatform 创建平台
func (h *ContentHandler) CreatePartPlatform(c *gin.Context) {
	log.Printf("收到创建平台请求")

	var platform model.PartPlatform
	if err := c.ShouldBindJSON(&platform); err != nil {
		log.Printf("请求参数错误: %v", err)
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	log.Printf("平台数据: %+v", platform)

	if err := h.contentService.CreatePartPlatform(&platform); err != nil {
		log.Printf("创建平台失败: %v", err)
		response.InternalServerError(c, "创建平台失败")
		return
	}

	log.Printf("平台创建成功，ID: %d", platform.ID)
	response.SuccessWithMsg(c, platform, "创建平台成功")
}

// UpdatePartPlatform 更新平台
func (h *ContentHandler) UpdatePartPlatform(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的平台ID")
		return
	}

	var platform model.PartPlatform
	if err := c.ShouldBindJSON(&platform); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	if err := h.contentService.UpdatePartPlatform(uint(id), &platform); err != nil {
		response.InternalServerError(c, "更新平台失败")
		return
	}

	response.SuccessWithMsg(c, platform, "更新平台成功")
}

// DeletePartPlatform 删除平台
func (h *ContentHandler) DeletePartPlatform(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的平台ID")
		return
	}

	if err := h.contentService.DeletePartPlatform(uint(id)); err != nil {
		response.InternalServerError(c, "删除平台失败")
		return
	}

	response.SuccessWithMsg(c, nil, "删除平台成功")
}

// GetProjectCases 获取项目案例列表（支持分页）
func (h *ContentHandler) GetProjectCases(c *gin.Context) {
	// 获取分页参数
	pageStr := c.DefaultQuery("page", "1")
	sizeStr := c.DefaultQuery("size", "10")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	size, err := strconv.Atoi(sizeStr)
	if err != nil || size < 1 || size > 100 {
		size = 10
	}

	// 注意：由于ProjectCase模型已优化，移除了搜索参数处理

	// 获取所有项目案例
	allCases, err := h.contentService.GetAllProjectCases()
	if err != nil {
		response.InternalServerError(c, "获取项目案例列表失败")
		return
	}

	// 过滤项目案例
	var filteredCases []model.ProjectCase
	for _, projectCase := range allCases {
		// 注意：由于ProjectCase模型已优化，移除了Title字段，无法进行关键词过滤

		filteredCases = append(filteredCases, projectCase)
	}

	// 计算总数
	total := len(filteredCases)

	// 计算分页
	offset := (page - 1) * size
	end := offset + size

	// 处理边界情况
	if offset >= total {
		result := map[string]interface{}{
			"list":      []model.ProjectCase{},
			"total":     total,
			"page":      page,
			"page_size": size,
		}
		response.Success(c, result)
		return
	}

	if end > total {
		end = total
	}

	// 分页数据
	pagedCases := filteredCases[offset:end]

	// 构造响应数据
	result := map[string]interface{}{
		"list":      pagedCases,
		"total":     total,
		"page":      page,
		"page_size": size,
	}

	response.Success(c, result)
}

// GetProjectCaseDetail 获取项目案例详情
func (h *ContentHandler) GetProjectCaseDetail(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的项目案例ID")
		return
	}

	projectCase, err := h.contentService.GetProjectCaseByID(uint(id))
	if err != nil {
		response.InternalServerError(c, "获取项目案例详情失败")
		return
	}
	response.Success(c, projectCase)
}

// CreateProjectCase 创建项目案例
func (h *ContentHandler) CreateProjectCase(c *gin.Context) {
	var projectCase model.ProjectCase
	if err := c.ShouldBindJSON(&projectCase); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	if err := h.contentService.CreateProjectCase(&projectCase); err != nil {
		response.InternalServerError(c, "创建项目案例失败")
		return
	}

	response.SuccessWithMsg(c, projectCase, "创建项目案例成功")
}

// UpdateProjectCase 更新项目案例
func (h *ContentHandler) UpdateProjectCase(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的项目案例ID")
		return
	}

	var projectCase model.ProjectCase
	if err := c.ShouldBindJSON(&projectCase); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	if err := h.contentService.UpdateProjectCase(uint(id), &projectCase); err != nil {
		response.InternalServerError(c, "更新项目案例失败")
		return
	}

	response.SuccessWithMsg(c, projectCase, "更新项目案例成功")
}

// DeleteProjectCase 删除项目案例
func (h *ContentHandler) DeleteProjectCase(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的项目案例ID")
		return
	}

	if err := h.contentService.DeleteProjectCase(uint(id)); err != nil {
		response.InternalServerError(c, "删除项目案例失败")
		return
	}

	response.SuccessWithMsg(c, nil, "删除项目案例成功")
}

// UpdateProjectCaseSort 更新项目案例排序
func (h *ContentHandler) UpdateProjectCaseSort(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的项目案例ID")
		return
	}

	var req struct {
		Sort int `json:"sort" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	// 获取现有项目案例
	projectCase, err := h.contentService.GetProjectCaseByID(uint(id))
	if err != nil {
		response.InternalServerError(c, "获取项目案例失败")
		return
	}

	// 注意：由于ProjectCase模型已优化，移除了Sort字段，排序功能已不可用
	// 如果需要排序功能，建议重新设计数据结构
	response.BadRequest(c, "排序功能已移除，请联系开发人员")

	response.SuccessWithMsg(c, projectCase, "更新排序成功")
}

// ==================== 平台扩展数据相关API ====================

// GetPartPlatformExtensions 获取平台扩展数据列表
func (h *ContentHandler) GetPartPlatformExtensions(c *gin.Context) {
	platformIDStr := c.Param("platformId")
	platformID, err := strconv.ParseUint(platformIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的平台ID")
		return
	}

	extensions, err := h.contentService.GetPartPlatformExtensionsByPlatformID(uint(platformID))
	if err != nil {
		response.InternalServerError(c, "获取扩展数据失败")
		return
	}
	response.Success(c, extensions)
}

// CreatePartPlatformExtension 创建平台扩展数据
func (h *ContentHandler) CreatePartPlatformExtension(c *gin.Context) {
	var extension model.PartPlatformExtension
	if err := c.ShouldBindJSON(&extension); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	if err := h.contentService.CreatePartPlatformExtension(&extension); err != nil {
		response.InternalServerError(c, "创建扩展数据失败")
		return
	}
	response.SuccessWithMsg(c, extension, "创建扩展数据成功")
}

// UpdatePartPlatformExtension 更新平台扩展数据
func (h *ContentHandler) UpdatePartPlatformExtension(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的扩展数据ID")
		return
	}

	var extension model.PartPlatformExtension
	if err := c.ShouldBindJSON(&extension); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	if err := h.contentService.UpdatePartPlatformExtension(uint(id), &extension); err != nil {
		response.InternalServerError(c, "更新扩展数据失败")
		return
	}
	response.SuccessWithMsg(c, extension, "更新扩展数据成功")
}

// DeletePartPlatformExtension 删除平台扩展数据
func (h *ContentHandler) DeletePartPlatformExtension(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的扩展数据ID")
		return
	}

	if err := h.contentService.DeletePartPlatformExtension(uint(id)); err != nil {
		response.InternalServerError(c, "删除扩展数据失败")
		return
	}
	response.SuccessWithMsg(c, nil, "删除扩展数据成功")
}

// CreatePartPlatformTable 创建平台表格
func (h *ContentHandler) CreatePartPlatformTable(c *gin.Context) {
	var table model.PartPlatformTable
	if err := c.ShouldBindJSON(&table); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	if err := h.contentService.CreatePartPlatformTable(&table); err != nil {
		response.InternalServerError(c, "创建表格失败")
		return
	}
	response.SuccessWithMsg(c, table, "创建表格成功")
}

// UpdatePartPlatformTable 更新平台表格
func (h *ContentHandler) UpdatePartPlatformTable(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的表格ID")
		return
	}

	var table model.PartPlatformTable
	if err := c.ShouldBindJSON(&table); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	if err := h.contentService.UpdatePartPlatformTable(uint(id), &table); err != nil {
		response.InternalServerError(c, "更新表格失败")
		return
	}
	response.SuccessWithMsg(c, table, "更新表格成功")
}

// DeletePartPlatformTable 删除平台表格
func (h *ContentHandler) DeletePartPlatformTable(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的表格ID")
		return
	}

	if err := h.contentService.DeletePartPlatformTable(uint(id)); err != nil {
		response.InternalServerError(c, "删除表格失败")
		return
	}
	response.SuccessWithMsg(c, nil, "删除表格成功")
}

// GetPartPlatformTableColumns 获取表格列配置（合并模板和旧数据）
func (h *ContentHandler) GetPartPlatformTableColumns(c *gin.Context) {
	tableIDStr := c.Param("tableId")
	tableID, err := strconv.ParseUint(tableIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的表格ID")
		return
	}

	// 尝试获取基于模板的列配置
	templateColumns, overrides, err := h.contentService.GetTableColumnsWithTemplate(uint(tableID))
	if err == nil && len(templateColumns) > 0 {
		// 如果有模板配置，转换为统一格式返回
		var result []map[string]interface{}

		// 添加模板列
		for _, col := range templateColumns {
			columnData := map[string]interface{}{
				"id":           col.ID,
				"table_id":     tableID,
				"column_name":  col.Name,
				"column_label": col.Label,
				"column_type":  col.Type,
				"sort_order":   col.SortOrder,
				"is_template":  true,
			}
			result = append(result, columnData)
		}

		// 添加覆盖配置
		for _, override := range overrides {
			if override.ColumnTemplate != nil {
				columnData := map[string]interface{}{
					"id":           override.ID,
					"table_id":     tableID,
					"column_name":  override.ColumnTemplate.Name,
					"column_label": override.CustomLabel,
					"column_type":  override.ColumnTemplate.Type,
					"sort_order":   override.SortOrder,
					"is_override":  true,
					"action":       override.Action,
				}
				if override.CustomLabel == "" {
					columnData["column_label"] = override.ColumnTemplate.Label
				}
				result = append(result, columnData)
			}
		}

		response.Success(c, result)
		return
	}

	// 如果没有模板配置，回退到旧的列配置
	columns, err := h.contentService.GetPartPlatformTableColumnsByTableID(uint(tableID))
	if err != nil {
		response.InternalServerError(c, "获取表格列配置失败")
		return
	}
	response.Success(c, columns)
}

// CreatePartPlatformTableColumn 创建表格列
func (h *ContentHandler) CreatePartPlatformTableColumn(c *gin.Context) {
	var column model.PartPlatformTableColumn
	if err := c.ShouldBindJSON(&column); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	if err := h.contentService.CreatePartPlatformTableColumn(&column); err != nil {
		response.InternalServerError(c, "创建表格列失败")
		return
	}
	response.SuccessWithMsg(c, column, "创建表格列成功")
}

// UpdatePartPlatformTableColumn 更新表格列
func (h *ContentHandler) UpdatePartPlatformTableColumn(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的表格列ID")
		return
	}

	var column model.PartPlatformTableColumn
	if err := c.ShouldBindJSON(&column); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	if err := h.contentService.UpdatePartPlatformTableColumn(uint(id), &column); err != nil {
		response.InternalServerError(c, "更新表格列失败")
		return
	}
	response.SuccessWithMsg(c, column, "更新表格列成功")
}

// DeletePartPlatformTableColumn 删除表格列
func (h *ContentHandler) DeletePartPlatformTableColumn(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的表格列ID")
		return
	}

	if err := h.contentService.DeletePartPlatformTableColumn(uint(id)); err != nil {
		response.InternalServerError(c, "删除表格列失败")
		return
	}
	response.SuccessWithMsg(c, nil, "删除表格列成功")
}

// GetPartPlatformTableRows 获取表格行数据
func (h *ContentHandler) GetPartPlatformTableRows(c *gin.Context) {
	tableIDStr := c.Param("tableId")
	tableID, err := strconv.ParseUint(tableIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的表格ID")
		return
	}

	rows, err := h.contentService.GetPartPlatformTableRowsByTableID(uint(tableID))
	if err != nil {
		response.InternalServerError(c, "获取表格行数据失败")
		return
	}
	response.Success(c, rows)
}

// CreatePartPlatformTableRow 创建表格行
func (h *ContentHandler) CreatePartPlatformTableRow(c *gin.Context) {
	var row model.PartPlatformTableRow
	if err := c.ShouldBindJSON(&row); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	if err := h.contentService.CreatePartPlatformTableRow(&row); err != nil {
		response.InternalServerError(c, "创建表格行失败")
		return
	}
	response.SuccessWithMsg(c, row, "创建表格行成功")
}

// UpdatePartPlatformTableRow 更新表格行
func (h *ContentHandler) UpdatePartPlatformTableRow(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的表格行ID")
		return
	}

	var row model.PartPlatformTableRow
	if err := c.ShouldBindJSON(&row); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	if err := h.contentService.UpdatePartPlatformTableRow(uint(id), &row); err != nil {
		response.InternalServerError(c, "更新表格行失败")
		return
	}
	response.SuccessWithMsg(c, row, "更新表格行成功")
}

// DeletePartPlatformTableRow 删除表格行
func (h *ContentHandler) DeletePartPlatformTableRow(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的表格行ID")
		return
	}

	if err := h.contentService.DeletePartPlatformTableRow(uint(id)); err != nil {
		response.InternalServerError(c, "删除表格行失败")
		return
	}
	response.SuccessWithMsg(c, nil, "删除表格行成功")
}
