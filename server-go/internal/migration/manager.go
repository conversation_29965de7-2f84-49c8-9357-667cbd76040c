package migration

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/pressly/goose/v3"
	"gorm.io/gorm"
)

// Status 迁移状态
type Status struct {
	Version   int64     `json:"version"`
	IsApplied bool      `json:"is_applied"`
	Source    string    `json:"source"`
	AppliedAt time.Time `json:"applied_at,omitempty"`
}

// Manager 迁移管理器接口
type Manager interface {
	// Up 执行所有待执行的迁移
	Up(ctx context.Context) error
	
	// UpTo 执行到指定版本的迁移
	UpTo(ctx context.Context, version int64) error
	
	// Down 回滚一个版本
	Down(ctx context.Context) error
	
	// DownTo 回滚到指定版本
	DownTo(ctx context.Context, version int64) error
	
	// Status 获取迁移状态
	Status(ctx context.Context) ([]Status, error)
	
	// Version 获取当前版本
	Version(ctx context.Context) (int64, error)
	
	// Validate 验证迁移文件
	Validate(ctx context.Context) error
	
	// Reset 重置所有迁移
	Reset(ctx context.Context) error
}

// GooseManager Goose迁移管理器实现
type GooseManager struct {
	db     *sql.DB
	gormDB *gorm.DB
	config *Config
}

// NewGooseManager 创建Goose迁移管理器
func NewGooseManager(gormDB *gorm.DB, config *Config) (*GooseManager, error) {
	sqlDB, err := gormDB.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get sql.DB from gorm: %w", err)
	}

	// 设置Goose配置
	if err := goose.SetDialect("mysql"); err != nil {
		return nil, fmt.Errorf("failed to set goose dialect: %w", err)
	}

	// 设置迁移表名
	goose.SetTableName(config.TableName)

	return &GooseManager{
		db:     sqlDB,
		gormDB: gormDB,
		config: config,
	}, nil
}

// Up 执行所有待执行的迁移
func (m *GooseManager) Up(ctx context.Context) error {
	if !m.config.Enabled {
		return fmt.Errorf("migration is disabled")
	}

	// 设置超时
	if m.config.Timeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, time.Duration(m.config.Timeout)*time.Second)
		defer cancel()
	}

	// 使用嵌入式迁移或文件系统迁移
	if m.config.UseEmbedded {
		return goose.Up(m.db, ".")
	}
	
	return goose.Up(m.db, m.config.Directory)
}

// UpTo 执行到指定版本的迁移
func (m *GooseManager) UpTo(ctx context.Context, version int64) error {
	if !m.config.Enabled {
		return fmt.Errorf("migration is disabled")
	}

	if m.config.UseEmbedded {
		return goose.UpTo(m.db, ".", version)
	}
	
	return goose.UpTo(m.db, m.config.Directory, version)
}

// Down 回滚一个版本
func (m *GooseManager) Down(ctx context.Context) error {
	if !m.config.Enabled {
		return fmt.Errorf("migration is disabled")
	}

	if m.config.UseEmbedded {
		return goose.Down(m.db, ".")
	}
	
	return goose.Down(m.db, m.config.Directory)
}

// DownTo 回滚到指定版本
func (m *GooseManager) DownTo(ctx context.Context, version int64) error {
	if !m.config.Enabled {
		return fmt.Errorf("migration is disabled")
	}

	if m.config.UseEmbedded {
		return goose.DownTo(m.db, ".", version)
	}
	
	return goose.DownTo(m.db, m.config.Directory, version)
}

// Status 获取迁移状态
func (m *GooseManager) Status(ctx context.Context) ([]Status, error) {
	var statuses []Status
	
	// 这里需要查询goose_db_version表获取状态
	// 简化实现，实际可以更详细
	version, err := m.Version(ctx)
	if err != nil {
		return nil, err
	}
	
	statuses = append(statuses, Status{
		Version:   version,
		IsApplied: true,
		Source:    "goose",
		AppliedAt: time.Now(),
	})
	
	return statuses, nil
}

// Version 获取当前版本
func (m *GooseManager) Version(ctx context.Context) (int64, error) {
	if m.config.UseEmbedded {
		return goose.GetDBVersion(m.db)
	}
	
	return goose.GetDBVersion(m.db)
}

// Validate 验证迁移文件
func (m *GooseManager) Validate(ctx context.Context) error {
	// 这里可以添加迁移文件的验证逻辑
	// 比如检查文件格式、SQL语法等
	return nil
}

// Reset 重置所有迁移
func (m *GooseManager) Reset(ctx context.Context) error {
	if !m.config.Enabled {
		return fmt.Errorf("migration is disabled")
	}

	if m.config.UseEmbedded {
		return goose.Reset(m.db, ".")
	}
	
	return goose.Reset(m.db, m.config.Directory)
}
