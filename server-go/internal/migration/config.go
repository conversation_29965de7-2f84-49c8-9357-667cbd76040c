package migration

import (
	"os"
	"strconv"
)

// Config 迁移配置
type Config struct {
	// 是否启用Goose迁移
	Enabled bool `json:"enabled"`
	
	// 迁移表名
	TableName string `json:"table_name"`
	
	// 迁移文件目录
	Directory string `json:"directory"`
	
	// 运行环境
	Environment string `json:"environment"`
	
	// 是否启用AutoMigrate作为fallback（仅开发环境）
	AutoMigrateFallback bool `json:"auto_migrate_fallback"`
	
	// 是否使用嵌入式迁移文件
	UseEmbedded bool `json:"use_embedded"`
	
	// 迁移超时时间（秒）
	Timeout int `json:"timeout"`
	
	// 是否在启动时自动执行迁移
	AutoRun bool `json:"auto_run"`
	
	// 是否启用事务
	EnableTransaction bool `json:"enable_transaction"`
}

// NewConfig 创建默认配置
func NewConfig() *Config {
	return &Config{
		Enabled:             getEnvBool("MIGRATION_ENABLED", true),
		TableName:           getEnvString("MIGRATION_TABLE_NAME", "goose_db_version"),
		Directory:           getEnvString("MIGRATION_DIRECTORY", "migrations"),
		Environment:         getEnvString("APP_MODE", "development"),
		AutoMigrateFallback: getEnvBool("MIGRATION_AUTO_MIGRATE_FALLBACK", true),
		UseEmbedded:         getEnvBool("MIGRATION_USE_EMBEDDED", true),
		Timeout:             getEnvInt("MIGRATION_TIMEOUT", 300),
		AutoRun:             getEnvBool("MIGRATION_AUTO_RUN", true),
		EnableTransaction:   getEnvBool("MIGRATION_ENABLE_TRANSACTION", true),
	}
}

// IsProduction 判断是否为生产环境
func (c *Config) IsProduction() bool {
	return c.Environment == "production"
}

// IsDevelopment 判断是否为开发环境
func (c *Config) IsDevelopment() bool {
	return c.Environment == "development"
}

// ShouldUseAutoMigrate 判断是否应该使用AutoMigrate
func (c *Config) ShouldUseAutoMigrate() bool {
	// 只有在开发环境且启用fallback时才使用AutoMigrate
	return c.IsDevelopment() && c.AutoMigrateFallback && !c.Enabled
}

// getEnvString 获取环境变量字符串值
func getEnvString(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvBool 获取环境变量布尔值
func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if parsed, err := strconv.ParseBool(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}

// getEnvInt 获取环境变量整数值
func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if parsed, err := strconv.Atoi(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}
