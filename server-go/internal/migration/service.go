package migration

import (
	"context"
	"fmt"
	"log"
	"time"

	"gorm.io/gorm"
	"weishi-server/internal/model"
)

// Service 迁移服务
type Service struct {
	manager Manager
	gormDB  *gorm.DB
	config  *Config
}

// NewService 创建迁移服务
func NewService(gormDB *gorm.DB, config *Config) (*Service, error) {
	manager, err := NewGooseManager(gormDB, config)
	if err != nil {
		return nil, fmt.Errorf("failed to create migration manager: %w", err)
	}

	return &Service{
		manager: manager,
		gormDB:  gormDB,
		config:  config,
	}, nil
}

// Initialize 初始化数据库迁移
func (s *Service) Initialize(ctx context.Context) error {
	log.Printf("🔄 开始初始化数据库迁移...")
	log.Printf("📊 迁移配置: Enabled=%v, Environment=%s, UseEmbedded=%v", 
		s.config.Enabled, s.config.Environment, s.config.UseEmbedded)

	// 如果迁移被禁用，检查是否应该使用AutoMigrate
	if !s.config.Enabled {
		if s.config.ShouldUseAutoMigrate() {
			log.Printf("⚠️  迁移已禁用，使用GORM AutoMigrate作为fallback")
			return s.runAutoMigrate()
		}
		log.Printf("ℹ️  迁移已禁用，跳过数据库迁移")
		return nil
	}

	// 验证迁移文件
	if err := s.manager.Validate(ctx); err != nil {
		return fmt.Errorf("migration validation failed: %w", err)
	}

	// 检查当前版本
	currentVersion, err := s.manager.Version(ctx)
	if err != nil {
		log.Printf("⚠️  无法获取当前迁移版本，可能是首次运行: %v", err)
		currentVersion = 0
	}
	log.Printf("📋 当前数据库版本: %d", currentVersion)

	// 如果配置了自动运行迁移
	if s.config.AutoRun {
		log.Printf("🚀 开始执行数据库迁移...")
		startTime := time.Now()
		
		if err := s.manager.Up(ctx); err != nil {
			return fmt.Errorf("failed to run migrations: %w", err)
		}
		
		duration := time.Since(startTime)
		log.Printf("✅ 数据库迁移完成，耗时: %v", duration)

		// 获取新版本
		newVersion, err := s.manager.Version(ctx)
		if err != nil {
			log.Printf("⚠️  无法获取新的迁移版本: %v", err)
		} else {
			log.Printf("📋 迁移后数据库版本: %d", newVersion)
		}
	} else {
		log.Printf("ℹ️  自动迁移已禁用，请手动执行迁移")
	}

	return nil
}

// runAutoMigrate 运行GORM AutoMigrate（仅开发环境fallback）
func (s *Service) runAutoMigrate() error {
	log.Printf("🔧 运行GORM AutoMigrate...")
	
	return s.gormDB.AutoMigrate(
		// 用户相关
		&model.AdminUser{},
		&model.AdminRole{},
		&model.AdminPermission{},
		&model.AdminUserRole{},
		&model.AdminRolePermission{},
		&model.AdminLog{},

		// 内容管理
		&model.Swiper{},
		&model.News{},
		&model.ProjectCase{},
		&model.Partner{},
		&model.FriendLink{},
		&model.Recruitment{},
		&model.PartPlatform{},
		&model.FileUpload{},
	)
}

// GetStatus 获取迁移状态
func (s *Service) GetStatus(ctx context.Context) ([]Status, error) {
	if !s.config.Enabled {
		return []Status{{
			Version:   0,
			IsApplied: false,
			Source:    "gorm_auto_migrate",
		}}, nil
	}

	return s.manager.Status(ctx)
}

// GetVersion 获取当前版本
func (s *Service) GetVersion(ctx context.Context) (int64, error) {
	if !s.config.Enabled {
		return 0, nil
	}

	return s.manager.Version(ctx)
}

// Up 执行迁移
func (s *Service) Up(ctx context.Context) error {
	if !s.config.Enabled {
		return fmt.Errorf("migration is disabled")
	}

	return s.manager.Up(ctx)
}

// Down 回滚迁移
func (s *Service) Down(ctx context.Context) error {
	if !s.config.Enabled {
		return fmt.Errorf("migration is disabled")
	}

	return s.manager.Down(ctx)
}

// UpTo 迁移到指定版本
func (s *Service) UpTo(ctx context.Context, version int64) error {
	if !s.config.Enabled {
		return fmt.Errorf("migration is disabled")
	}

	return s.manager.UpTo(ctx, version)
}

// DownTo 回滚到指定版本
func (s *Service) DownTo(ctx context.Context, version int64) error {
	if !s.config.Enabled {
		return fmt.Errorf("migration is disabled")
	}

	return s.manager.DownTo(ctx, version)
}

// Reset 重置所有迁移
func (s *Service) Reset(ctx context.Context) error {
	if !s.config.Enabled {
		return fmt.Errorf("migration is disabled")
	}

	return s.manager.Reset(ctx)
}

// HealthCheck 健康检查
func (s *Service) HealthCheck(ctx context.Context) error {
	if !s.config.Enabled {
		return nil
	}

	// 检查迁移表是否存在
	_, err := s.manager.Version(ctx)
	if err != nil {
		return fmt.Errorf("migration health check failed: %w", err)
	}

	return nil
}
