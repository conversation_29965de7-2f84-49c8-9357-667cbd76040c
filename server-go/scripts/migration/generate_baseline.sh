#!/bin/bash

# 生成基线迁移脚本
# 用于从现有数据库生成初始迁移文件

set -e

# 配置
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-3306}"
DB_USER="${DB_USER:-root}"
DB_PASS="${DB_PASS:-Ydb3344%}"
DB_NAME="${DB_NAME:-weizhi}"
MIGRATION_DIR="${MIGRATION_DIR:-migrations}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_step "检查依赖..."
    
    if ! command -v mysqldump &> /dev/null; then
        log_error "mysqldump 未安装"
        exit 1
    fi
    
    if ! command -v mysql &> /dev/null; then
        log_error "mysql 客户端未安装"
        exit 1
    fi
    
    log_info "✅ 依赖检查通过"
}

# 测试数据库连接
test_connection() {
    log_step "测试数据库连接..."
    
    if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" -e "USE $DB_NAME;" 2>/dev/null; then
        log_info "✅ 数据库连接成功"
    else
        log_error "❌ 数据库连接失败"
        exit 1
    fi
}

# 创建迁移目录
create_migration_dir() {
    log_step "创建迁移目录..."
    
    if [ ! -d "$MIGRATION_DIR" ]; then
        mkdir -p "$MIGRATION_DIR"
        log_info "✅ 创建迁移目录: $MIGRATION_DIR"
    else
        log_info "ℹ️  迁移目录已存在: $MIGRATION_DIR"
    fi
}

# 生成数据库结构
generate_schema() {
    log_step "生成数据库结构..."
    
    local output_file="$MIGRATION_DIR/00001_initial_schema.sql"
    local temp_file="/tmp/schema_dump.sql"
    
    # 导出数据库结构（不包含数据）
    mysqldump -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" \
        --no-data \
        --routines \
        --triggers \
        --single-transaction \
        --set-gtid-purged=OFF \
        "$DB_NAME" > "$temp_file"
    
    # 生成Goose格式的迁移文件
    cat > "$output_file" << 'EOF'
-- +goose Up
-- 初始化数据库schema，基于现有数据库生成
-- 生成时间: $(date)

EOF
    
    # 处理mysqldump输出，移除不需要的内容
    grep -v "^--" "$temp_file" | \
    grep -v "^/\*" | \
    grep -v "^$" | \
    sed 's/AUTO_INCREMENT=[0-9]* //g' >> "$output_file"
    
    cat >> "$output_file" << 'EOF'

-- +goose Down
-- 回滚时删除所有表（谨慎使用）
EOF
    
    # 生成删除表的语句
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" \
        -e "SELECT CONCAT('DROP TABLE IF EXISTS \`', table_name, '\`;') FROM information_schema.tables WHERE table_schema = '$DB_NAME' ORDER BY table_name DESC;" \
        -s -N >> "$output_file"
    
    rm -f "$temp_file"
    log_info "✅ 基线迁移文件已生成: $output_file"
}

# 生成数据迁移（可选）
generate_data_migration() {
    log_step "生成数据迁移..."
    
    local output_file="$MIGRATION_DIR/00002_initial_data.go"
    
    cat > "$output_file" << 'EOF'
package migrations

import (
	"database/sql"
	"fmt"

	"github.com/pressly/goose/v3"
)

func init() {
	goose.AddMigration(upInitialData, downInitialData)
}

// upInitialData 初始化基础数据
func upInitialData(tx *sql.Tx) error {
	// 这里可以添加初始化数据的逻辑
	// 例如：创建默认管理员用户、角色、权限等
	
	// 示例：创建默认管理员角色
	_, err := tx.Exec(`
		INSERT IGNORE INTO admin_roles (name, code, description, status, sort, created_at, updated_at) 
		VALUES ('超级管理员', 'super_admin', '系统超级管理员角色', 'active', 1, NOW(), NOW())
	`)
	if err != nil {
		return fmt.Errorf("failed to create default admin role: %w", err)
	}

	return nil
}

// downInitialData 回滚初始化数据
func downInitialData(tx *sql.Tx) error {
	// 删除初始化的数据
	_, err := tx.Exec("DELETE FROM admin_roles WHERE code = 'super_admin'")
	if err != nil {
		return fmt.Errorf("failed to remove default admin role: %w", err)
	}

	return nil
}
EOF
    
    log_info "✅ 数据迁移文件已生成: $output_file"
}

# 验证生成的迁移文件
validate_migration() {
    log_step "验证迁移文件..."
    
    # 检查SQL语法（简单验证）
    local schema_file="$MIGRATION_DIR/00001_initial_schema.sql"
    
    if [ -f "$schema_file" ]; then
        # 检查文件是否包含必要的Goose注释
        if grep -q "-- +goose Up" "$schema_file" && grep -q "-- +goose Down" "$schema_file"; then
            log_info "✅ 迁移文件格式验证通过"
        else
            log_error "❌ 迁移文件格式不正确"
            exit 1
        fi
    else
        log_error "❌ 迁移文件不存在"
        exit 1
    fi
}

# 主函数
main() {
    log_info "🚀 开始生成基线迁移..."
    
    check_dependencies
    test_connection
    create_migration_dir
    generate_schema
    generate_data_migration
    validate_migration
    
    log_info "✅ 基线迁移生成完成！"
    log_info "📁 迁移文件位置: $MIGRATION_DIR"
    log_info "📋 下一步："
    log_info "   1. 检查生成的迁移文件"
    log_info "   2. 根据需要调整迁移内容"
    log_info "   3. 测试迁移: go run cmd/migrate/main.go -command=status"
}

# 执行主函数
main "$@"
