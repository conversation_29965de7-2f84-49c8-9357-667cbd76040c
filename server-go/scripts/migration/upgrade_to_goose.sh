#!/bin/bash

# 从旧迁移系统升级到Goose的升级脚本
# 用于平滑迁移现有的数据库状态到新的Goose迁移系统

set -e

# 配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
SERVER_DIR="$PROJECT_ROOT"

# 数据库配置
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-3306}"
DB_USER="${DB_USER:-root}"
DB_PASS="${DB_PASS:-Ydb3344%}"
DB_NAME="${DB_NAME:-weizhi}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "从旧迁移系统升级到Goose"
    echo ""
    echo "用法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --dry-run       仅检查状态，不执行升级"
    echo "  --force         强制执行升级（跳过确认）"
    echo "  --backup        执行前备份数据库"
    echo "  --help          显示此帮助信息"
    echo ""
    echo "环境变量:"
    echo "  DB_HOST         数据库主机 (默认: localhost)"
    echo "  DB_PORT         数据库端口 (默认: 3306)"
    echo "  DB_USER         数据库用户 (默认: root)"
    echo "  DB_PASS         数据库密码 (默认: Ydb3344%)"
    echo "  DB_NAME         数据库名称 (默认: weizhi)"
}

# 检查数据库连接
check_database_connection() {
    log_step "检查数据库连接..."
    
    if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" -e "USE $DB_NAME;" 2>/dev/null; then
        log_info "✅ 数据库连接成功"
    else
        log_error "❌ 数据库连接失败"
        exit 1
    fi
}

# 检查当前迁移状态
check_current_migration_status() {
    log_step "检查当前迁移状态..."
    
    # 检查是否存在Goose迁移表
    local goose_table_exists=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" -D"$DB_NAME" -se "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = '$DB_NAME' AND table_name = 'goose_db_version'")
    
    if [ "$goose_table_exists" -gt 0 ]; then
        log_info "🔍 发现Goose迁移表，检查版本..."
        local current_version=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" -D"$DB_NAME" -se "SELECT version_id FROM goose_db_version ORDER BY id DESC LIMIT 1" 2>/dev/null || echo "0")
        log_info "📋 当前Goose版本: $current_version"
        return 0
    fi
    
    # 检查旧迁移系统的状态标志
    log_info "🔍 检查旧迁移系统状态..."
    
    # 检查001版本标志（表结构优化）
    local services_table_exists=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" -D"$DB_NAME" -se "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = '$DB_NAME' AND table_name = 'our_services'")
    local services_columns=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" -D"$DB_NAME" -se "SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = '$DB_NAME' AND table_name = 'our_services' AND column_name IN ('name', 'type', 'description')" 2>/dev/null || echo "0")
    
    if [ "$services_table_exists" -gt 0 ] && [ "$services_columns" -eq 0 ]; then
        log_info "✅ 检测到001版本已执行（表结构已优化）"
    else
        log_warn "⚠️  001版本可能未执行或部分执行"
    fi
    
    # 检查002版本标志（services表删除）
    local services_exists=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" -D"$DB_NAME" -se "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = '$DB_NAME' AND table_name = 'services'")
    
    if [ "$services_exists" -eq 0 ]; then
        log_info "✅ 检测到002版本已执行（services表已删除）"
    else
        log_warn "⚠️  002版本可能未执行，services表仍存在"
    fi
    
    # 检查003版本标志（测试迁移）
    local test_table_exists=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" -D"$DB_NAME" -se "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = '$DB_NAME' AND table_name = 'migration_test'")
    
    if [ "$test_table_exists" -gt 0 ]; then
        log_info "✅ 检测到003版本已执行（测试表存在）"
    else
        log_warn "⚠️  003版本可能未执行"
    fi
}

# 备份数据库
backup_database() {
    log_step "备份数据库..."
    
    local backup_file="backup_goose_upgrade_$(date +%Y%m%d_%H%M%S).sql"
    
    mysqldump -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" \
        --single-transaction \
        --routines \
        --triggers \
        --set-gtid-purged=OFF \
        "$DB_NAME" > "$backup_file"
    
    if [ -f "$backup_file" ]; then
        log_info "✅ 数据库备份完成: $backup_file"
    else
        log_error "❌ 数据库备份失败"
        exit 1
    fi
}

# 初始化Goose迁移表
initialize_goose() {
    log_step "初始化Goose迁移系统..."
    
    cd "$SERVER_DIR"
    
    # 构建迁移CLI工具
    if [ ! -f "build/migrate" ]; then
        log_info "构建迁移CLI工具..."
        go build -o build/migrate cmd/migrate/main.go
    fi
    
    # 设置环境变量启用迁移
    export MIGRATION_ENABLED=true
    export MIGRATION_USE_EMBEDDED=true
    export MIGRATION_AUTO_RUN=false
    
    # 检查Goose状态
    log_info "检查Goose迁移状态..."
    if ./build/migrate -command=version 2>/dev/null; then
        log_info "✅ Goose已初始化"
    else
        log_info "🔧 初始化Goose迁移表..."
        # 手动创建Goose版本表
        mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" -D"$DB_NAME" -e "
            CREATE TABLE IF NOT EXISTS goose_db_version (
                id int(11) NOT NULL AUTO_INCREMENT,
                version_id bigint(20) NOT NULL,
                is_applied tinyint(1) NOT NULL,
                tstamp timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                UNIQUE KEY goose_db_version_id_idx (version_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        "
    fi
}

# 标记已完成的迁移
mark_completed_migrations() {
    log_step "标记已完成的迁移..."
    
    # 标记基线迁移（00001）为已完成
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" -D"$DB_NAME" -e "
        INSERT IGNORE INTO goose_db_version (version_id, is_applied) VALUES (1, 1);
    "
    log_info "✅ 标记00001基线迁移为已完成"
    
    # 标记文件上传迁移（00002）为已完成
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" -D"$DB_NAME" -e "
        INSERT IGNORE INTO goose_db_version (version_id, is_applied) VALUES (2, 1);
    "
    log_info "✅ 标记00002文件上传迁移为已完成"
}

# 执行兼容性迁移
run_compatibility_migration() {
    log_step "执行兼容性迁移..."
    
    cd "$SERVER_DIR"
    
    # 执行00003兼容性迁移
    if ./build/migrate -command=up -version=3; then
        log_info "✅ 兼容性迁移执行成功"
    else
        log_error "❌ 兼容性迁移执行失败"
        exit 1
    fi
}

# 验证升级结果
verify_upgrade() {
    log_step "验证升级结果..."
    
    cd "$SERVER_DIR"
    
    # 检查Goose版本
    local current_version=$(./build/migrate -command=version 2>/dev/null || echo "0")
    log_info "📋 当前Goose版本: $current_version"
    
    # 检查迁移状态
    log_info "📊 迁移状态:"
    ./build/migrate -command=status
    
    # 验证关键表存在
    local key_tables=("admin_users" "admin_roles" "swipers" "news" "project_cases")
    for table in "${key_tables[@]}"; do
        local exists=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" -D"$DB_NAME" -se "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = '$DB_NAME' AND table_name = '$table'")
        if [ "$exists" -gt 0 ]; then
            log_info "✅ 表 $table 存在"
        else
            log_error "❌ 表 $table 不存在"
        fi
    done
}

# 主函数
main() {
    local dry_run=false
    local force=false
    local backup=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --dry-run)
                dry_run=true
                shift
                ;;
            --force)
                force=true
                shift
                ;;
            --backup)
                backup=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    log_info "🚀 开始从旧迁移系统升级到Goose"
    
    if [ "$dry_run" = true ]; then
        log_info "🔍 DRY RUN 模式 - 仅检查状态"
    fi
    
    # 执行升级步骤
    check_database_connection
    check_current_migration_status
    
    if [ "$dry_run" = true ]; then
        log_info "✅ DRY RUN 完成"
        exit 0
    fi
    
    # 确认升级
    if [ "$force" = false ]; then
        echo ""
        echo -e "${YELLOW}⚠️  即将执行Goose升级，这将：${NC}"
        echo "   1. 初始化Goose迁移系统"
        echo "   2. 标记已完成的迁移"
        echo "   3. 执行兼容性迁移"
        echo ""
        read -p "确认继续？(y/N): " confirm
        if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
            log_info "升级已取消"
            exit 0
        fi
    fi
    
    if [ "$backup" = true ]; then
        backup_database
    fi
    
    initialize_goose
    mark_completed_migrations
    run_compatibility_migration
    verify_upgrade
    
    log_info "✅ Goose升级完成！"
    log_info "📋 下一步："
    log_info "   1. 验证应用功能正常"
    log_info "   2. 使用新的Goose命令管理迁移"
    log_info "   3. 更新部署脚本"
}

# 执行主函数
main "$@"
