#!/bin/bash

# 验证迁移文件脚本
# 用于检查迁移文件的格式和语法

set -e

# 配置
MIGRATION_DIR="${MIGRATION_DIR:-migrations}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查迁移目录
check_migration_dir() {
    log_step "检查迁移目录..."
    
    if [ ! -d "$MIGRATION_DIR" ]; then
        log_error "迁移目录不存在: $MIGRATION_DIR"
        exit 1
    fi
    
    log_info "✅ 迁移目录存在: $MIGRATION_DIR"
}

# 验证SQL迁移文件
validate_sql_migration() {
    local file="$1"
    local errors=0
    
    log_step "验证SQL迁移文件: $(basename "$file")"
    
    # 检查文件是否包含必要的Goose注释
    if ! grep -q "-- +goose Up" "$file"; then
        log_error "缺少 '-- +goose Up' 注释"
        ((errors++))
    fi
    
    if ! grep -q "-- +goose Down" "$file"; then
        log_error "缺少 '-- +goose Down' 注释"
        ((errors++))
    fi
    
    # 检查Up部分是否在Down部分之前
    local up_line=$(grep -n "-- +goose Up" "$file" | cut -d: -f1)
    local down_line=$(grep -n "-- +goose Down" "$file" | cut -d: -f1)
    
    if [ -n "$up_line" ] && [ -n "$down_line" ] && [ "$up_line" -gt "$down_line" ]; then
        log_error "'-- +goose Up' 必须在 '-- +goose Down' 之前"
        ((errors++))
    fi
    
    # 检查SQL语法（基本检查）
    if grep -q "CREATE TABLE" "$file"; then
        # 检查CREATE TABLE语句是否有IF NOT EXISTS
        if ! grep -q "CREATE TABLE IF NOT EXISTS\|CREATE TABLE.*IF NOT EXISTS" "$file"; then
            log_warn "建议在CREATE TABLE语句中使用IF NOT EXISTS"
        fi
    fi
    
    # 检查是否有危险操作
    if grep -qi "DROP DATABASE\|TRUNCATE\|DELETE FROM.*WHERE.*1=1" "$file"; then
        log_warn "检测到潜在危险操作，请仔细检查"
    fi
    
    if [ $errors -eq 0 ]; then
        log_info "✅ SQL迁移文件验证通过"
    else
        log_error "❌ SQL迁移文件验证失败，发现 $errors 个错误"
        return 1
    fi
}

# 验证Go迁移文件
validate_go_migration() {
    local file="$1"
    local errors=0
    
    log_step "验证Go迁移文件: $(basename "$file")"
    
    # 检查包声明
    if ! grep -q "package migrations" "$file"; then
        log_error "缺少正确的包声明: package migrations"
        ((errors++))
    fi
    
    # 检查必要的导入
    if ! grep -q "github.com/pressly/goose/v3" "$file"; then
        log_error "缺少goose导入"
        ((errors++))
    fi
    
    # 检查init函数
    if ! grep -q "func init()" "$file"; then
        log_error "缺少init函数"
        ((errors++))
    fi
    
    # 检查goose.AddMigration调用
    if ! grep -q "goose.AddMigration" "$file"; then
        log_error "缺少goose.AddMigration调用"
        ((errors++))
    fi
    
    # 检查Up和Down函数
    if ! grep -q "func.*Up.*\*sql.Tx.*error" "$file"; then
        log_error "缺少Up函数或函数签名不正确"
        ((errors++))
    fi
    
    if ! grep -q "func.*Down.*\*sql.Tx.*error" "$file"; then
        log_error "缺少Down函数或函数签名不正确"
        ((errors++))
    fi
    
    # 检查Go语法
    if command -v go &> /dev/null; then
        if ! go vet "$file" 2>/dev/null; then
            log_warn "Go语法检查发现问题，请检查代码"
        fi
    fi
    
    if [ $errors -eq 0 ]; then
        log_info "✅ Go迁移文件验证通过"
    else
        log_error "❌ Go迁移文件验证失败，发现 $errors 个错误"
        return 1
    fi
}

# 验证迁移文件命名
validate_naming() {
    local file="$1"
    local filename=$(basename "$file")
    
    # 检查文件名格式：数字_描述.sql 或 数字_描述.go
    if [[ ! "$filename" =~ ^[0-9]{5}_[a-zA-Z0-9_]+\.(sql|go)$ ]]; then
        log_warn "文件名格式建议: 00001_description.sql 或 00001_description.go"
        log_warn "当前文件名: $filename"
    fi
}

# 检查迁移顺序
check_migration_order() {
    log_step "检查迁移文件顺序..."
    
    local files=($(find "$MIGRATION_DIR" -name "*.sql" -o -name "*.go" | grep -v "embed.go" | sort))
    local prev_num=0
    local errors=0
    
    for file in "${files[@]}"; do
        local filename=$(basename "$file")
        local num=$(echo "$filename" | grep -o '^[0-9]*')
        
        if [ -n "$num" ]; then
            if [ "$num" -le "$prev_num" ]; then
                log_error "迁移文件顺序错误: $filename (编号: $num, 前一个: $prev_num)"
                ((errors++))
            fi
            prev_num=$num
        fi
    done
    
    if [ $errors -eq 0 ]; then
        log_info "✅ 迁移文件顺序正确"
    else
        log_error "❌ 发现 $errors 个顺序错误"
        return 1
    fi
}

# 主验证函数
validate_all_migrations() {
    log_step "验证所有迁移文件..."
    
    local total_errors=0
    local files=($(find "$MIGRATION_DIR" -name "*.sql" -o -name "*.go" | grep -v "embed.go"))
    
    if [ ${#files[@]} -eq 0 ]; then
        log_warn "未找到迁移文件"
        return 0
    fi
    
    for file in "${files[@]}"; do
        validate_naming "$file"
        
        if [[ "$file" == *.sql ]]; then
            if ! validate_sql_migration "$file"; then
                ((total_errors++))
            fi
        elif [[ "$file" == *.go ]]; then
            if ! validate_go_migration "$file"; then
                ((total_errors++))
            fi
        fi
    done
    
    if ! check_migration_order; then
        ((total_errors++))
    fi
    
    return $total_errors
}

# 生成验证报告
generate_report() {
    local errors=$1
    
    log_step "生成验证报告..."
    
    echo "迁移文件验证报告" > validation_report.txt
    echo "==================" >> validation_report.txt
    echo "验证时间: $(date)" >> validation_report.txt
    echo "迁移目录: $MIGRATION_DIR" >> validation_report.txt
    echo "" >> validation_report.txt
    
    local files=($(find "$MIGRATION_DIR" -name "*.sql" -o -name "*.go" | grep -v "embed.go"))
    echo "迁移文件列表:" >> validation_report.txt
    for file in "${files[@]}"; do
        echo "  - $(basename "$file")" >> validation_report.txt
    done
    echo "" >> validation_report.txt
    
    if [ $errors -eq 0 ]; then
        echo "验证结果: ✅ 通过" >> validation_report.txt
    else
        echo "验证结果: ❌ 失败 ($errors 个错误)" >> validation_report.txt
    fi
    
    log_info "📄 验证报告已生成: validation_report.txt"
}

# 主函数
main() {
    log_info "🔍 开始验证迁移文件..."
    
    check_migration_dir
    
    if validate_all_migrations; then
        local errors=$?
        generate_report $errors
        
        if [ $errors -eq 0 ]; then
            log_info "✅ 所有迁移文件验证通过！"
            exit 0
        else
            log_error "❌ 验证失败，发现 $errors 个错误"
            exit 1
        fi
    else
        local errors=$?
        generate_report $errors
        log_error "❌ 验证失败，发现 $errors 个错误"
        exit 1
    fi
}

# 执行主函数
main "$@"
