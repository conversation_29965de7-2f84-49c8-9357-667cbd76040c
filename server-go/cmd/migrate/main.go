package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"os"
	"strconv"

	"weishi-server/internal/config"
	"weishi-server/internal/database"
	"weishi-server/internal/migration"

	// 导入迁移文件
	_ "weishi-server/migrations"
)

func main() {
	var (
		command = flag.String("command", "", "Migration command: up, down, status, version, reset")
		version = flag.String("version", "", "Target version for up-to or down-to commands")
		help    = flag.Bool("help", false, "Show help")
	)
	flag.Parse()

	if *help || *command == "" {
		showHelp()
		return
	}

	// 初始化配置
	cfg := config.New()

	// 初始化数据库连接
	db, err := database.New(cfg.Database)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// 创建迁移配置
	migrationConfig := migration.NewConfig()
	// 强制启用迁移（CLI工具总是使用迁移）
	migrationConfig.Enabled = true

	// 创建迁移服务
	migrationService, err := migration.NewService(db, migrationConfig)
	if err != nil {
		log.Fatalf("Failed to create migration service: %v", err)
	}

	ctx := context.Background()

	// 执行命令
	switch *command {
	case "up":
		if *version != "" {
			targetVersion, err := strconv.ParseInt(*version, 10, 64)
			if err != nil {
				log.Fatalf("Invalid version: %v", err)
			}
			err = migrationService.UpTo(ctx, targetVersion)
		} else {
			err = migrationService.Up(ctx)
		}
		if err != nil {
			log.Fatalf("Failed to run up migration: %v", err)
		}
		log.Println("✅ Migration up completed successfully")

	case "down":
		if *version != "" {
			targetVersion, err := strconv.ParseInt(*version, 10, 64)
			if err != nil {
				log.Fatalf("Invalid version: %v", err)
			}
			err = migrationService.DownTo(ctx, targetVersion)
		} else {
			err = migrationService.Down(ctx)
		}
		if err != nil {
			log.Fatalf("Failed to run down migration: %v", err)
		}
		log.Println("✅ Migration down completed successfully")

	case "status":
		statuses, err := migrationService.GetStatus(ctx)
		if err != nil {
			log.Fatalf("Failed to get migration status: %v", err)
		}
		
		fmt.Println("Migration Status:")
		fmt.Println("================")
		for _, status := range statuses {
			appliedStatus := "Pending"
			if status.IsApplied {
				appliedStatus = "Applied"
			}
			fmt.Printf("Version: %d, Status: %s, Source: %s\n", 
				status.Version, appliedStatus, status.Source)
		}

	case "version":
		currentVersion, err := migrationService.GetVersion(ctx)
		if err != nil {
			log.Fatalf("Failed to get current version: %v", err)
		}
		fmt.Printf("Current migration version: %d\n", currentVersion)

	case "reset":
		fmt.Print("⚠️  This will reset all migrations. Are you sure? (y/N): ")
		var confirm string
		fmt.Scanln(&confirm)
		if confirm != "y" && confirm != "Y" {
			log.Println("Operation cancelled")
			return
		}
		
		err = migrationService.Reset(ctx)
		if err != nil {
			log.Fatalf("Failed to reset migrations: %v", err)
		}
		log.Println("✅ Migration reset completed successfully")

	default:
		fmt.Printf("Unknown command: %s\n", *command)
		showHelp()
		os.Exit(1)
	}
}

func showHelp() {
	fmt.Println("Migration CLI Tool")
	fmt.Println("==================")
	fmt.Println()
	fmt.Println("Usage:")
	fmt.Println("  migrate -command=<command> [options]")
	fmt.Println()
	fmt.Println("Commands:")
	fmt.Println("  up              Apply all pending migrations")
	fmt.Println("  up -version=N   Apply migrations up to version N")
	fmt.Println("  down            Rollback one migration")
	fmt.Println("  down -version=N Rollback to version N")
	fmt.Println("  status          Show migration status")
	fmt.Println("  version         Show current migration version")
	fmt.Println("  reset           Reset all migrations (dangerous!)")
	fmt.Println()
	fmt.Println("Options:")
	fmt.Println("  -version=N      Target version for up-to or down-to commands")
	fmt.Println("  -help           Show this help message")
	fmt.Println()
	fmt.Println("Environment Variables:")
	fmt.Println("  MIGRATION_ENABLED=true/false")
	fmt.Println("  MIGRATION_TABLE_NAME=goose_db_version")
	fmt.Println("  MIGRATION_DIRECTORY=migrations")
	fmt.Println("  MIGRATION_USE_EMBEDDED=true/false")
	fmt.Println()
	fmt.Println("Examples:")
	fmt.Println("  migrate -command=up")
	fmt.Println("  migrate -command=up -version=2")
	fmt.Println("  migrate -command=status")
	fmt.Println("  migrate -command=down")
}
