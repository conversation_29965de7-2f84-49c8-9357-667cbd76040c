<template>
  <div class="w-full h-full">
    <main class="w-full flex flex-col items-center">
      <img
        class="w-full h-auto"
        alt=""
        src="https://images-1305444037.cos.ap-nanjing.myqcloud.com/background/3.jpg"
      />
      <div
        class="w-full box-images bg-center bg-repeat bg-cover flex justify-center px-10"
      >
        <div class="w-main-width flex flex-col justify-center">
          <div class="w-full mt-10">
            <p class="w-full text-center font-semibold">
              建立燃油系统零部件数据库，将系统相关零部件标准化，使客户的选择多样化、自由化。
            </p>
            <p class="w-full text-center text-sm my-1">
              To establish the database of fuel system parts, standardize the
              parts related to the system,
            </p>
            <p class="w-full text-center text-sm">
              diversify and liberalize the choice of customers.
            </p>
            <div class="w-full text-center text-2xl my-4">
              <i
                class="el-icon-d-arrow-right transform rotate-90"
                style="color: #0c77b3"
              ></i>
            </div>
          </div>
          <ClientOnly>
            <div class="w-full flex justify-end">
              <el-input
                placeholder="请输入内容"
                v-model="searchText"
                class="input-with-select"
                style="width: 30%"
              >
                <template #append>
                  <el-button @click="handleSearch">
                    <el-icon><Search /></el-icon>
                  </el-button>
                </template>
              </el-input>
            </div>
            <div
              class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8 my-6"
            >
              <div
                v-for="(item, index) in displayList"
                :key="index"
                style="height: 350px"
                class="box-border p-4 bg-white rounded-2xl flex flex-col items-center"
              >
                <div
                  class="w-1/2 h-1/2 bg-no-repeat bg-contain bg-center"
                  :style="{ backgroundImage: `url(${item.url})` }"
                ></div>
                <div class="text-center w-full h-10 mt-8">{{ item.name }}</div>
                <div class="w-full h-20 flex items-center justify-center">
                  <el-button style="margin-top: 10px" @click="toPartDisplay(item.id)"
                    >查看更多</el-button
                  >
                </div>
              </div>
            </div>
            <div class="w-full flex justify-center py-6">
              <el-pagination
                background
                layout="total,prev, pager, next"
                :total="total"
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-size="pageSize"
              >
              </el-pagination>
            </div>
          </ClientOnly>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { Search } from '@element-plus/icons-vue'
import type { PartPlatform } from '@weishi/types'
import { computed, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { usePartPlatforms } from '~/composables/useWebData'
import { useApi } from '~/utils/api'

const router = useRouter()
const api = useApi()
const searchText = ref('')
const currentPage = ref(1)
const pageSize = ref(9)
const total = ref(0)
const { data: allDataRaw, fetchData } = usePartPlatforms()
const allData = ref<PartPlatform[]>([])

// 计算当前页显示的数据
const displayList = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return allData.value.slice(start, end)
})

onMounted(async () => {
  await fetchData()
  // 兼容 data/list 两种格式
  const val = allDataRaw.value as any
  if (Array.isArray(val)) {
    allData.value = val
  } else if (Array.isArray(val?.list)) {
    allData.value = val.list
  } else {
    allData.value = []
  }
  total.value = allData.value.length
})

// 搜索处理
const handleSearch = async () => {
  if (searchText.value) {
    try {
      const response = await api.getPartPlatforms({
        page: 1,
        maxPage: pageSize.value
      })

      if (response?.data?.data?.list) {
        // 过滤搜索结果
        const filteredResults = response.data.data.list.filter((item: { name: string }) =>
          item.name.toLowerCase().includes(searchText.value.toLowerCase())
        )
        total.value = filteredResults.length
        allData.value = filteredResults
        currentPage.value = 1
      }
    } catch (error) {
      console.error('搜索失败:', error)
    }
  } else {
    await fetchData()
  }
}

// 页码改变处理
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchData()
}

// 跳转到详情页
const toPartDisplay = (id: number) => {
  if (!id) {
    console.warn('toPartDisplay 跳转 id 为空，已拦截');
    return;
  }
  router.push(`/part/${id}`)
}

definePageMeta({
  layout: 'main'
})
</script> 

<style lang="scss" scoped>
.img {
  overflow: hidden;
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-size: cover;
  background-image: url("https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/part/ban.png");
}
.box-images {
  background-image: url("https://images-1305444037.cos.ap-nanjing.myqcloud.com/part-platform/parts.png");
}
</style> 