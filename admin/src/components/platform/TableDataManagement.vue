<template>
  <div class="h-full flex flex-col">
    <!-- 操作区域 -->
    <div class="flex justify-between items-center mb-4">
      <div>
        <h4 class="font-medium">数据管理</h4>
        <p class="text-sm text-gray-500">共 {{ tableData.length }} 行数据</p>
      </div>
      <div class="flex gap-2">
        <el-button size="small" @click="refreshData">
          <i class="i-mdi-refresh mr-1"></i>
          刷新
        </el-button>
        <el-button size="small" type="primary" @click="addNewRow">
          <i class="i-mdi-plus mr-1"></i>
          添加数据
        </el-button>
        <el-button
          size="small"
          type="danger"
          :disabled="!selectedRows.length"
          @click="handleBatchDelete"
        >
          <i class="i-mdi-delete mr-1"></i>
          批量删除 ({{ selectedRows.length }})
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="flex-1 overflow-hidden">
      <div class="h-full overflow-auto">
      <div v-if="columns.length === 0" class="text-center py-12 text-gray-500">
        <i class="i-mdi-table-column text-4xl mb-2"></i>
        <p>请先配置表格列</p>
        <p class="text-sm">需要先在"列配置"中定义表格结构</p>
      </div>

      <div v-else-if="tableData.length === 0" class="text-center py-12 text-gray-500">
        <i class="i-mdi-database-plus text-4xl mb-2"></i>
        <p>暂无数据</p>
        <p class="text-sm">点击"添加数据"开始录入</p>
      </div>

      <el-table
        v-else
        :data="tableData"
        border
        style="width: 100%; min-width: max-content;"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column type="index" label="#" width="60" />
        
        <el-table-column
          v-for="column in sortedColumns"
          :key="column.id"
          :prop="column.column_name"
          :label="column.column_label"
          :min-width="getColumnWidth(column.column_type)"
        >
          <template #default="{ row }">
            <span v-if="column.column_type === 'boolean'">
              <el-tag :type="getRowValue(row, column.column_name) ? 'success' : 'danger'" size="small">
                {{ getRowValue(row, column.column_name) ? '是' : '否' }}
              </el-tag>
            </span>
            <span v-else-if="column.column_type === 'date'">
              {{ formatDate(getRowValue(row, column.column_name)) }}
            </span>
            <span v-else>
              {{ getRowValue(row, column.column_name) || '-' }}
            </span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row, $index }">
            <el-button size="small" @click="editRow(row, $index)">
              编辑
            </el-button>
            <el-button size="small" type="danger" @click="deleteRow(row, $index)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      </div>
    </div>

    <!-- 数据编辑对话框 -->
    <el-dialog
      v-model="dataDialogVisible"
      :title="editingRowIndex >= 0 ? '编辑数据' : '添加数据'"
      width="600px"
      @close="resetDataForm"
    >
      <el-form
        ref="dataFormRef"
        :model="editingRowData"
        :rules="dataFormRules"
        label-width="120px"
      >
        <el-form-item
          v-for="column in sortedColumns"
          :key="column.id"
          :label="column.column_label"
          :prop="column.column_name"
        >
          <!-- 文本输入 -->
          <el-input
            v-if="column.column_type === 'text'"
            v-model="editingRowData[column.column_name]"
            :placeholder="`请输入${column.column_label}`"
          />
          
          <!-- 长文本输入 -->
          <el-input
            v-else-if="column.column_type === 'textarea'"
            v-model="editingRowData[column.column_name]"
            type="textarea"
            :rows="3"
            :placeholder="`请输入${column.column_label}`"
          />
          
          <!-- 数字输入 -->
          <el-input-number
            v-else-if="column.column_type === 'number'"
            v-model="editingRowData[column.column_name]"
            :placeholder="`请输入${column.column_label}`"
            style="width: 100%"
          />
          
          <!-- 日期输入 -->
          <el-date-picker
            v-else-if="column.column_type === 'date'"
            v-model="editingRowData[column.column_name]"
            type="date"
            :placeholder="`请选择${column.column_label}`"
            style="width: 100%"
          />
          
          <!-- 布尔值输入 -->
          <el-switch
            v-else-if="column.column_type === 'boolean'"
            v-model="editingRowData[column.column_name]"
            active-text="是"
            inactive-text="否"
          />
          
          <!-- 默认文本输入 -->
          <el-input
            v-else
            v-model="editingRowData[column.column_name]"
            :placeholder="`请输入${column.column_label}`"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="flex justify-end gap-2">
          <el-button @click="dataDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveRowData" :loading="saveLoading">
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { platformConfigApi } from '@/api/platform-config';
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus';
import { computed, onMounted, reactive, ref, watch } from 'vue';

// Props
interface Props {
  tableId: number
  columns?: any[]  // 改为可选，因为我们会动态获取
}

const props = defineProps<Props>()

// 响应式数据
const tableData = ref<any[]>([])
const columns = ref<any[]>([])  // 新增：动态获取的列配置
const dataDialogVisible = ref(false)
const saveLoading = ref(false)
const editingRowIndex = ref(-1)
const selectedRows = ref<any[]>([])

// 表单相关
const dataFormRef = ref<FormInstance>()
const editingRowData = reactive<Record<string, any>>({})

// 计算属性
const sortedColumns = computed(() => {
  return [...columns.value].sort((a, b) => a.sort_order - b.sort_order)
})

const dataFormRules = computed(() => {
  const rules: FormRules = {}
  columns.value.forEach(column => {
    if (column.is_required) {
      rules[column.column_name] = [
        { required: true, message: `请输入${column.column_label}`, trigger: 'blur' }
      ]
    }
  })
  return rules
})

// 方法
const getColumnWidth = (type: string) => {
  const widthMap: Record<string, number> = {
    text: 120,
    number: 100,
    date: 120,
    boolean: 80,
    textarea: 200
  }
  return widthMap[type] || 120
}

const formatDate = (date: string) => {
  if (!date) return '-'
  return new Date(date).toLocaleDateString('zh-CN')
}

// 获取行数据中指定列的值
const getRowValue = (row: any, columnName: string) => {
  try {
    // 如果row_data是字符串，解析JSON
    if (typeof row.row_data === 'string') {
      const parsedData = JSON.parse(row.row_data)
      return parsedData[columnName] || '-'
    }
    // 如果row_data是对象，直接访问
    else if (typeof row.row_data === 'object' && row.row_data) {
      return row.row_data[columnName] || '-'
    }
    // 兼容旧格式：直接从row对象获取
    else {
      return row[columnName] || '-'
    }
  } catch (error) {
    console.error('解析行数据失败:', error)
    return '-'
  }
}

const addNewRow = () => {
  if (props.columns.length === 0) {
    ElMessage.warning('请先配置表格列')
    return
  }
  
  resetDataForm()
  editingRowIndex.value = -1
  dataDialogVisible.value = true
}

const editRow = (row: any, index: number) => {
  // 清空编辑数据
  Object.keys(editingRowData).forEach(key => {
    delete editingRowData[key]
  })

  try {
    // 解析row_data中的JSON数据
    let rowData = {}
    if (typeof row.row_data === 'string') {
      rowData = JSON.parse(row.row_data)
    } else if (typeof row.row_data === 'object' && row.row_data) {
      rowData = row.row_data
    } else {
      // 兼容旧格式
      rowData = { ...row }
      delete rowData.id
      delete rowData.table_id
      delete rowData.sort_order
      delete rowData.created_at
      delete rowData.updated_at
    }

    // 将解析后的数据赋值给编辑表单
    Object.assign(editingRowData, rowData)
  } catch (error) {
    console.error('解析编辑数据失败:', error)
    ElMessage.error('数据格式错误，无法编辑')
    return
  }

  editingRowIndex.value = index
  dataDialogVisible.value = true
}

const deleteRow = async (row: any, index: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这行数据吗？', '确认删除', {
      type: 'warning'
    })

    // 调用删除API
    await platformConfigApi.deleteTableRow(row.id)
    tableData.value.splice(index, 1)
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const saveRowData = async () => {
  if (!dataFormRef.value) return
  
  try {
    await dataFormRef.value.validate()
    saveLoading.value = true
    
    const rowData = { ...editingRowData }

    if (editingRowIndex.value >= 0) {
      // 更新现有数据
      const existingRow = tableData.value[editingRowIndex.value]
      await platformConfigApi.updateTableRow(existingRow.id, {
        data: rowData,
        sort_order: existingRow.sort_order || 0
      })
      tableData.value[editingRowIndex.value] = { ...existingRow, ...rowData }
    } else {
      // 添加新数据
      const response = await platformConfigApi.createTableRow({
        table_id: props.tableId,
        data: rowData,
        sort_order: tableData.value.length
      })
      tableData.value.push(response.data)
    }
    
    ElMessage.success(editingRowIndex.value >= 0 ? '更新成功' : '添加成功')
    dataDialogVisible.value = false
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saveLoading.value = false
  }
}

// 选择变化处理
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// 批量删除
const handleBatchDelete = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的数据')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 条数据吗？`,
      '批量删除确认',
      {
        type: 'warning',
        confirmButtonText: '确定删除',
        cancelButtonText: '取消'
      }
    )

    const selectedIds = selectedRows.value.map(row => row.id)

    // 逐个删除（后端没有批量删除API）
    for (const id of selectedIds) {
      await platformConfigApi.deleteTableRow(id)
    }

    // 从本地数据中移除
    tableData.value = tableData.value.filter(row => !selectedIds.includes(row.id))
    selectedRows.value = []

    ElMessage.success(`成功删除 ${selectedIds.length} 条数据`)
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

const resetDataForm = () => {
  // 清空表单数据
  Object.keys(editingRowData).forEach(key => {
    delete editingRowData[key]
  })
  
  // 为每个列设置默认值
  props.columns.forEach(column => {
    switch (column.column_type) {
      case 'number':
        editingRowData[column.column_name] = null
        break
      case 'boolean':
        editingRowData[column.column_name] = false
        break
      case 'date':
        editingRowData[column.column_name] = null
        break
      default:
        editingRowData[column.column_name] = ''
    }
  })
  
  dataFormRef.value?.clearValidate()
}

const refreshData = () => {
  loadTableData()
  ElMessage.success('数据已刷新')
}

const loadColumns = async () => {
  if (!props.tableId) return

  try {
    const response = await platformConfigApi.getTableColumns(props.tableId)
    columns.value = response || []
  } catch (error) {
    ElMessage.error('加载列配置失败')
    console.error('加载列配置失败:', error)
    columns.value = []
  }
}

const loadTableData = async () => {
  if (!props.tableId) return

  try {
    // 调用真实API
    const data = await platformConfigApi.getTableRows(props.tableId)
    tableData.value = data || []
  } catch (error) {
    ElMessage.error('加载数据失败')
    console.error('加载数据失败:', error)
    tableData.value = []
  }
}

const loadData = async () => {
  if (!props.tableId) return

  // 先加载列配置，再加载数据
  await loadColumns()
  await loadTableData()
}

// 监听tableId变化，重新加载数据
watch(() => props.tableId, () => {
  if (props.tableId) {
    loadData()
  }
}, { immediate: true })

onMounted(() => {
  if (props.tableId) {
    loadData()
  }
})
</script>

<style scoped>
.flex-1 {
  flex: 1;
}

.h-full {
  height: 100%;
}
</style>
